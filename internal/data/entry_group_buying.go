package data

import (
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/now"
	"github.com/samber/lo"
	"gitlab.yc345.tv/backend/channel/internal/biz/entity"
	"gitlab.yc345.tv/backend/channel/internal/biz/iface"
	"gitlab.yc345.tv/backend/channel/internal/common"
	"gitlab.yc345.tv/backend/channel/internal/common/regionhelper"
	"gitlab.yc345.tv/backend/channel/internal/conf"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_dao"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_model"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_repo"
	"gitlab.yc345.tv/backend/channel/internal/pkg"
	"gitlab.yc345.tv/backend/channel/internal/pkg/middleware/token"
	"gitlab.yc345.tv/backend/utils/v2/client/redis"
	"strings"
	"time"
)

type entryGroupBuyingRepo struct {
	channel_repo.IEntryGroupBuyingRepo
	cache     redis.IClient
	log       *log.Helper
	config    *conf.Bootstrap
	data      *Data
	userRepo  iface.IUserRepo
	entryRepo iface.IEntryRepo
}

func NewEntryGroupBuyingRepo(cache redis.IClient, data *Data, logger log.Logger, config *conf.Bootstrap,
	repo *channel_repo.EntryGroupBuyingRepo,
	userRepo iface.IUserRepo,
	entryRepo iface.IEntryRepo,
) iface.IEntryGroupBuyingRepo {
	return &entryGroupBuyingRepo{
		IEntryGroupBuyingRepo: repo,
		cache:                 cache,
		log:                   log.NewHelper(logger),
		config:                config,
		data:                  data,
		userRepo:              userRepo,
		entryRepo:             entryRepo,
	}
}

func (e *entryGroupBuyingRepo) GetGroupBuyingListByParams(ctx context.Context, params *entity.GroupBuyingListParam) (int64, []*channel_model.EntryGroupBuying, error) {
	query := channel_dao.Use(e.data.DB()).EntryGroupBuying
	db := e.data.DB().WithContext(ctx).Model(&channel_model.EntryGroupBuying{})
	entryQuery := channel_dao.Use(e.data.DB()).Entry
	switch params.Role {
	case common.RoleAgency:
		// 代理商只查找agency_id为当前代理商订单
		db = db.Where(`("entryGroupBuyings"."agency_id" = ? or "entryGroupBuyings"."create_user_id" = ?)`, params.AgencyId, params.RoleId)
	case common.RoleAgencyEmployee:
		db = db.Where(fmt.Sprintf(`("entryGroupBuyings".%s in (?) and "entryGroupBuyings".%s = ?) or "entryGroupBuyings".%s = ?`, query.SchoolID.ColumnName().String(), query.AgencyID.ColumnName().String(), query.CreateUserID.ColumnName().String()), params.ShortCodes, params.AgencyId, params.RoleId)
	default:
		if len(params.ShortCodes) > 0 {
			db = db.Where(query.SchoolID.In(params.ShortCodes...))
		}
		if params.AgencyId != 0 {
			db = db.Where(query.AgencyID.Eq(params.AgencyId))
		}
	}
	if params.Phone != "" {
		phone := pkg.Base64Encode(params.Phone)
		userQuery := channel_dao.Use(e.data.DB()).User
		user := channel_model.User{}
		err := e.data.DB().WithContext(ctx).Model(&channel_model.User{}).Where(userQuery.Phone.Eq(phone)).Where(userQuery.Role.Eq(common.RoleAgency)).Find(&user).Error
		if err != nil {
			return 0, nil, err
		}
		if user.ID == "" {
			return 0, nil, nil
		}
		db = db.Where(query.CreateUserID.Eq(user.ID))
	}
	if len(params.StageSubject) > 0 {
		ss := fmt.Sprintf("{%s}", strings.Join(params.StageSubject, ","))
		db = db.Where(fmt.Sprintf("%s <@ ?", query.StageSubject.ColumnName().String()), ss)
	}
	if params.SearchRegion != "" {
		db = db.Where(regionhelper.RegionsToSequlizeSQL(query.Region.ColumnName().String(), []string{params.SearchRegion}, true))
	}
	if params.EntryId != 0 {
		db = db.Where(query.EntryID.Eq(params.EntryId))
	}
	if params.RoomRef != "" {
		db = db.Where(query.RoomRef.Eq(params.RoomRef))
	}
	if params.SchoolId != 0 {
		var schoolIds []int32
		if len(params.ShortCodes) > 0 {
			schoolIds = lo.Intersect(params.ShortCodes, []int32{params.SchoolId})
		} else {
			schoolIds = []int32{params.SchoolId}
		}
		if len(schoolIds) == 0 {
			return 0, nil, nil
		}
		db = db.Where(query.SchoolID.In(schoolIds...))
	}
	if params.ValidateDate != "" {
		db = db.Where(fmt.Sprintf("%s >= ?", query.ValidateDate.ColumnName().String()), params.ValidateDate)
	}
	if params.ExpireDate != "" {
		db = db.Where(fmt.Sprintf("%s <= ?", query.ExpireDate.ColumnName().String()), params.ExpireDate)
	}
	entryState := int32(0)
	now := time.Now().Local().Format("2006-01-02 15:04:05")
	if params.State != "" {
		switch params.State {
		case "pre_check":
			db = db.Where(fmt.Sprintf("%s >= ?", query.ExpireDate.ColumnName().String()), now)
			entryState = 1
		case "pre_start":
			db = db.Where(fmt.Sprintf("%s > ?", query.ValidateDate.ColumnName().String()), now)
			entryState = 3
		case "starting":
			db = db.Where(fmt.Sprintf("%s < ?", query.ValidateDate.ColumnName().String()), now).Where(fmt.Sprintf("%s > ?", query.ExpireDate.ColumnName().String()), now)
			entryState = 3
		case "fail":
			entryState = 2
		case "end":
			db = db.Where(fmt.Sprintf("%s <= ?", query.ExpireDate.ColumnName().String()), now)
			entryState = 3
		case "expire":
			db = db.Where(fmt.Sprintf("%s < ?", query.ExpireDate.ColumnName().String()), now)
			entryState = 1
		case "cancel":
			entryState = 4
		default:
			db = db.Where(fmt.Sprintf("%s > ?", query.ExpireDate.ColumnName().String()), now)
			entryState = 1
		}
	}
	if params.BeforeBeginDate != "" {
		db = db.Where(fmt.Sprintf("%s <= ?", query.ValidateDate.ColumnName().String()), params.BeforeBeginDate)
	}
	entryDb := e.data.DB().Where(` "Detail".type = ?`, 3)
	if entryState != 0 {
		entryDb = entryDb.Where(` "Detail".state = ?`, entryState)
	}
	db = db.Joins("Detail", entryDb)
	db = db.Where(entryDb)
	orderBy := fmt.Sprintf("%s desc", query.CreatedDate.ColumnName().String())
	if params.Sort != "" {
		orderBy = params.Sort
	}
	db = db.Order(orderBy)
	total := int64(0)
	err := db.Count(&total).Error
	if err != nil {
		return 0, nil, err
	}
	if params.Offset > 0 {
		db = db.Offset(params.Offset)
	}
	db = db.Limit(lo.If(params.Limit == 0, 20).Else(params.Limit))
	var list []*channel_model.EntryGroupBuying
	err = db.Find(&list).Error
	if err != nil {
		return 0, nil, err
	}
	entryIds := lo.Map(list, func(g *channel_model.EntryGroupBuying, _ int) int64 {
		return g.EntryID
	})
	var entries []*channel_model.Entry
	err = e.data.DB().WithContext(ctx).Model(&channel_model.Entry{}).Where(entryQuery.ID.In(entryIds...)).Find(&entries).Error
	if err != nil {
		return 0, nil, err
	}
	entryMap := lo.SliceToMap(entries, func(entry *channel_model.Entry) (int64, *channel_model.Entry) {
		return entry.ID, entry
	})
	for i := 0; i < len(list); i++ {
		item := list[i]
		list[i].Detail = entryMap[item.EntryID]
	}
	return total, list, nil
}

func (e *entryGroupBuyingRepo) FindOneWithGoodsByEntryId(ctx context.Context, entryId int64) (*channel_model.EntryGroupBuying, error) {
	query := channel_dao.Use(e.data.DB()).EntryGroupBuying
	res := channel_model.EntryGroupBuying{}
	err := e.data.DB().WithContext(ctx).Model(&res).Where(query.EntryID.Eq(entryId)).
		Preload(query.Detail.Name()).Preload(query.Goods.Name()).Preload(query.CancelUserOfGroupBuying.Name()).
		Find(&res).Error
	if err != nil {
		return nil, err
	}
	return &res, nil
}
func (e *entryGroupBuyingRepo) GetGroupBuyCountOneYear(ctx context.Context, roomRef string, stageSubjects []string, agencyId int64) (int64, error) {
	query := channel_dao.Use(e.data.DB()).EntryGroupBuying
	entryQuery := channel_dao.Use(e.data.DB()).Entry
	db := e.data.DB().WithContext(ctx).Model(&channel_model.EntryGroupBuying{})
	db = db.Where(query.CreatedDate.Gte(now.BeginningOfYear())).Where(query.CreatedDate.Lt(now.EndOfYear()))
	if roomRef != "" {
		db = db.Where(query.RoomRef.Eq(roomRef))
	}
	if len(stageSubjects) > 0 {
		db = db.Where(fmt.Sprintf("%s && ?", query.StageSubject.ColumnName().String()), fmt.Sprintf("{%s}", strings.Join(stageSubjects, ",")))
	}
	if agencyId != 0 {
		db = db.Where(query.AgencyID.Eq(agencyId))
	}
	// 通过审核的不能超过三个
	count := int64(0)
	err := db.Preload(query.Detail.Name(), fmt.Sprintf("%s = ?", entryQuery.State.ColumnName().String()), common.EntryStateAuditPass).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil

}

func (e *entryGroupBuyingRepo) UpdateGroupBuy(ctx context.Context, groupBuy *channel_model.EntryGroupBuying, user *token.User, state int32) error {
	var err error
	tx := channel_dao.Use(e.data.DB()).Begin()
	defer func() {
		if err != nil {
			rErr := tx.Rollback()
			if rErr != nil {
				e.log.Errorf("rollback failed, %v", rErr)
			}
			return
		}
		cErr := tx.Commit()
		if cErr != nil {
			e.log.Errorf("commit failed, %v", cErr)
		}
	}()
	entry := &channel_model.Entry{
		ID: groupBuy.EntryID,
	}
	if user.IsGearbox {
		groupBuy.GearboxCancelUserID = user.ID
		groupBuy.GearboxCancelUserName = user.Name
		entry.GearboxUpdateUserID = user.ID
		entry.GearboxUpdateUserName = user.Name
	} else {
		groupBuy.CancelUserID = &user.ID
		entry.UpdateUserID = &user.ID
	}
	err = e.UpdateOneByTx(ctx, tx.Query, groupBuy)
	if err != nil {
		return err
	}
	if state == 0 {
		return nil
	}
	// 修改状态
	entry.State = state
	err = e.entryRepo.UpdateOneByTx(ctx, tx.Query, entry)
	return err
}

func (e *entryGroupBuyingRepo) CreateOneWithoutFields(ctx context.Context, data *channel_model.EntryGroupBuying, fields []string) error {
	dao := e.data.DB().WithContext(ctx).Model(data)
	err := dao.Omit(fields...).Create(data).Error
	if err != nil {
		return err
	}
	return nil
}
