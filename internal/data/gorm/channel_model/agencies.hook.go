package channel_model

import (
	"gitlab.yc345.tv/backend/channel/internal/common"
	"gorm.io/gorm"
)

func (t *Agency) BeforeSave(tx *gorm.DB) error {
	return t.encrypt(tx)
}

func (t *Agency) AfterSave(tx *gorm.DB) error {
	return t.decode(tx)
}

func (t *Agency) AfterFind(tx *gorm.DB) error {
	return t.decode(tx)
}

func (t *Agency) encrypt(tx *gorm.DB) error {
	// 实现加密
	if t == nil || (t.SignedManPh == "" && t.CustomerPh == "") {
		return tx.Error
	}
	if t.SignedManPh != "" {
		phone, err := common.EncryptSensitive(t.SignedManPh)
		if err != nil {
			return err
		}
		t.SignedManPh = phone
	}
	if t.CustomerPh != "" {
		phone, err := common.EncryptSensitive(t.CustomerPh)
		if err != nil {
			return err
		}
		t.CustomerPh = phone
	}
	return tx.Error
}

func (t *Agency) decode(tx *gorm.DB) error {
	// 实现解密
	if t == nil || (t.SignedManPh == "" && t.CustomerPh == "") {
		return tx.Error
	}
	if t.SignedManPh != "" {
		phone, err := common.DecryptSensitive(t.SignedManPh)
		if err != nil {
			return err
		}
		t.SignedManPh = phone
	}
	if t.CustomerPh != "" {
		phone, err := common.DecryptSensitive(t.CustomerPh)
		if err != nil {
			return err
		}
		t.CustomerPh = phone
	}
	return tx.Error
}
