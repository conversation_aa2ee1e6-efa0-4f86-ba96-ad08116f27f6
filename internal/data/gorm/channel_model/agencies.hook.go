package channel_model

import (
	"gitlab.yc345.tv/backend/channel/internal/pkg/common"
	"gorm.io/gorm"
)

func (t *Agency) BeforeSave(tx *gorm.DB) error {
	return t.encrypt(tx)
}

func (t *Agency) AfterSave(tx *gorm.DB) error {
	return t.decode(tx)
}

func (t *Agency) AfterFind(tx *gorm.DB) error {
	return t.decode(tx)
}

func (t *Agency) encrypt(tx *gorm.DB) error {
	// 实现加密
	if t == nil || (t.SignedManPh == "" && t.CustomerPh == "") {
		return nil
	}
	if t.SignedManPh != "" {
		phone, err := common.EncryptSensitive(t.SignedManPh)
		if err != nil {
			return err
		}
		t.SignedManPh = phone
	}
	if t.CustomerPh != "" {
		phone, err := common.EncryptSensitive(t.CustomerPh)
		if err != nil {
			return err
		}
		t.CustomerPh = phone
	}
	return nil
}

func (t *Agency) decode(tx *gorm.DB) error {
	// 实现解密
	if t == nil || (t.SignedManPh == "" && t.CustomerPh == "") {
		return nil
	}
	if t.SignedManPh != "" {
		phone, err := common.DecryptSensitive(t.SignedManPh)
		if err != nil {
			return err
		}
		t.SignedManPh = phone
	}
	if t.CustomerPh != "" {
		phone, err := common.DecryptSensitive(t.CustomerPh)
		if err != nil {
			return err
		}
		t.CustomerPh = phone
	}
	return nil
}

// EncryptSensitiveFields 加密敏感字段，用于绕过 hook 的操作
func (t *Agency) EncryptSensitiveFields() error {
	if t == nil {
		return nil
	}

	if t.SignedManPh != "" {
		encrypted, err := common.EncryptSensitive(t.SignedManPh)
		if err != nil {
			return err
		}
		t.SignedManPh = encrypted
	}

	if t.CustomerPh != "" {
		encrypted, err := common.EncryptSensitive(t.CustomerPh)
		if err != nil {
			return err
		}
		t.CustomerPh = encrypted
	}

	return nil
}

// DecryptSensitiveFields 解密敏感字段，用于绕过 hook 的操作
func (t *Agency) DecryptSensitiveFields() error {
	if t == nil {
		return nil
	}

	if t.SignedManPh != "" {
		decrypted, err := common.DecryptSensitive(t.SignedManPh)
		if err != nil {
			return err
		}
		t.SignedManPh = decrypted
	}

	if t.CustomerPh != "" {
		decrypted, err := common.DecryptSensitive(t.CustomerPh)
		if err != nil {
			return err
		}
		t.CustomerPh = decrypted
	}

	return nil
}
