// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package channel_model

import (
	"database/sql"
	"time"

	"github.com/lib/pq"
)

const TableNameAgency = "agencies"

// Agency mapped from table <agencies>
type Agency struct {
	ID                           int64          `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true;comment:代理商ID" json:"id"`                                          // 代理商ID
	Name                         string         `gorm:"column:name;type:character varying(40);not null;comment:代理商名称" json:"name"`                                            // 代理商名称
	ChargeName                   string         `gorm:"column:charge_name;type:character varying(20);not null;comment:负责人名称" json:"chargeName"`                               // 负责人名称
	ChargePhone                  string         `gorm:"column:charge_phone;type:character varying(50);not null;comment:负责人电话" json:"chargePhone"`                             // 负责人电话
	RegionType                   int32          `gorm:"column:region_type;type:integer;comment:代理商类型" json:"regionType"`                                                      // 代理商类型
	Level                        int32          `gorm:"column:level;type:integer;comment:代理商级别（旧，已废弃，后续删除）" json:"level"`                                                     // 代理商级别（旧，已废弃，后续删除）
	StageSubject                 pq.StringArray `gorm:"column:stage_subject;type:character varying(5)[];comment:学科学段" json:"stageSubject"`                                    // 学科学段
	CreatedDate                  time.Time      `gorm:"column:created_date;type:timestamp with time zone;not null;comment:创建时间" json:"createdDate"`                           // 创建时间
	CreateUserID                 string         `gorm:"column:create_user_id;type:uuid;not null;comment:创建人" json:"createUserId"`                                             // 创建人
	UpdatedDate                  sql.NullTime   `gorm:"column:updated_date;type:timestamp with time zone;comment:代理商更新时间" json:"updatedDate"`                                 // 代理商更新时间
	UpdateUserID                 *string        `gorm:"column:update_user_id;type:uuid;comment:最近更改人" json:"updateUserId"`                                                    // 最近更改人
	Deleted                      bool           `gorm:"column:deleted;type:boolean;comment:是否被删除" json:"deleted"`                                                             // 是否被删除
	Status                       bool           `gorm:"column:status;type:boolean;comment:活跃状态" json:"status"`                                                                // 活跃状态
	EnterpriseName               string         `gorm:"column:enterprise_name;type:character varying(50);comment:企业名称" json:"enterpriseName"`                                 // 企业名称
	EnterpriseType               string         `gorm:"column:enterprise_type;type:character varying(50);comment:企业类型" json:"enterpriseType"`                                 // 企业类型
	EnterpriseLegalPerson        string         `gorm:"column:enterprise_legal_person;type:character varying(20);comment:企业法人" json:"enterpriseLegalPerson"`                  // 企业法人
	EnterpriseAddress            string         `gorm:"column:enterprise_address;type:character varying(100);comment:企业地址" json:"enterpriseAddress"`                          // 企业地址
	TaxpayerIdentificationNumber string         `gorm:"column:taxpayer_identification_number;type:character varying(255);comment:纳税人识别号" json:"taxpayerIdentificationNumber"` // 纳税人识别号
	InvoiceTitle                 string         `gorm:"column:invoice_title;type:character varying(255);comment:发票抬头" json:"invoiceTitle"`                                    // 发票抬头
	BusinessLicense              string         `gorm:"column:business_license;type:character varying(255);comment:营业执照" json:"businessLicense"`                              // 营业执照
	TaxpayerQualification        string         `gorm:"column:taxpayer_qualification;type:character varying(255);comment:纳税人资质证明" json:"taxpayerQualification"`               // 纳税人资质证明
	Remark                       string         `gorm:"column:remark;type:character varying(1000);comment:备注" json:"remark"`                                                  // 备注
	IDPhoto                      string         `gorm:"column:ID_Photo;type:character varying(255)" json:"idPhoto"`
	CustomerPhone                string         `gorm:"column:customer_phone;type:character varying(20);comment:客服电话" json:"customerPhone"`                     // 客服电话
	OldName                      string         `gorm:"column:old_name;type:character varying(100);comment:代理商曾用名" json:"oldName"`                              // 代理商曾用名
	FinancePhone                 string         `gorm:"column:finance_phone;type:character varying(50);comment:财务对接人电话" json:"financePhone"`                    // 财务对接人电话
	BusinessPhone                string         `gorm:"column:business_phone;type:character varying(50);comment:业务对接人电话" json:"businessPhone"`                  // 业务对接人电话
	SchoolReserveCount           int32          `gorm:"column:school_reserve_count;type:integer;comment:学校储备数量" json:"schoolReserveCount"`                      // 学校储备数量
	OnionEmployeeCount           int32          `gorm:"column:onion_employee_count;type:integer;comment:负责洋葱员工数量" json:"onionEmployeeCount"`                    // 负责洋葱员工数量
	GuestDesc                    string         `gorm:"column:guest_desc;type:text;comment:客情关系描述" json:"guestDesc"`                                            // 客情关系描述
	OtherProduct                 string         `gorm:"column:other_product;type:text;comment:代理其他产品情况" json:"otherProduct"`                                    // 代理其他产品情况
	TeamDesc                     string         `gorm:"column:team_desc;type:text;comment:团队建设情况" json:"teamDesc"`                                              // 团队建设情况
	CooperationFeeFile           string         `gorm:"column:cooperation_fee_file;type:character varying(255);comment:合作费截图" json:"cooperationFeeFile"`        // 合作费截图
	ReceiverAddress              string         `gorm:"column:receiver_address;type:character varying(50);comment:收件人地址" json:"receiverAddress"`                // 收件人地址
	SignedMan                    string         `gorm:"column:signed_man;type:character varying(20);comment:代理签约人姓名" json:"signedMan"`                          // 代理签约人姓名
	SignedManPhone               string         `gorm:"column:signed_man_phone;type:character varying(20);comment:代理签约人电话" json:"signedManPhone"`               // 代理签约人电话
	PrepayImg                    string         `gorm:"column:prepay_img;type:character varying(255);comment:预付款截图" json:"prepayImg"`                           // 预付款截图
	BaseInfo                     string         `gorm:"column:base_info;type:text;comment:代理商基本信息" json:"baseInfo"`                                             // 代理商基本信息
	AgencyLevel                  string         `gorm:"column:agency_level;type:character varying(10);comment:代理商级别（新）" json:"agencyLevel"`                     // 代理商级别（新）
	AgencyType                   string         `gorm:"column:agency_type;type:character varying(20);comment:代理商类型，agent：合作代理商、direct：直营代理商" json:"agencyType"` // 代理商类型，agent：合作代理商、direct：直营代理商
	Eml                          string         `gorm:"column:eml;type:character varying;not null;comment:邮箱" json:"eml"`                                       // 邮箱
	FeishuVendorID               string         `gorm:"column:feishu_vendor_id;type:character varying;comment:飞书交易方id" json:"feishuVendorId"`                   // 飞书交易方id
	FeishuVendor                 string         `gorm:"column:feishu_vendor;type:character varying;comment:飞书交易方编码" json:"feishuVendor"`                        // 飞书交易方编码
	TaxpayersType                string         `gorm:"column:taxpayers_type;type:enum_agencies_taxpayers_type;comment:纳税人类型" json:"taxpayersType"`             // 纳税人类型
	SignedManPh                  string         `gorm:"column:signed_man_ph;type:character varying;comment:代理签约人电话" json:"signedManPh"`                        // 代理签约人电话
	CustomerPh                   string         `gorm:"column:customer_ph;type:character varying;comment:客服电话" json:"customerPh"`                               // 客服电话
}

// TableName Agency's table name
func (*Agency) TableName() string {
	return TableNameAgency
}
