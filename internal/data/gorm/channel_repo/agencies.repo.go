// Code generated by gen/repo. DO NOT EDIT.
// Code generated by gen/repo. DO NOT EDIT.
// Code generated by gen/repo. DO NOT EDIT.

package channel_repo

import (
	"context"
	"encoding/json"
	"errors"

	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_dao"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_model"
	"gitlab.yc345.tv/backend/utils/v2/orm"
	"gitlab.yc345.tv/backend/utils/v2/orm/gen/cache"
	"gitlab.yc345.tv/backend/utils/v2/orm/gen/condition"
	"gitlab.yc345.tv/backend/utils/v2/orm/gen/custom"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var _ IAgencyRepo = (*AgencyRepo)(nil)

var (
	CacheAgencyByIDPrefix = "DBCache:channel:AgencyByID"
)

type (
	IAgencyRepo interface {
		// CreateOne 创建一条数据
		CreateOne(ctx context.Context, data *channel_model.Agency) error
		// CreateOneByTx 创建一条数据(事务)
		CreateOneByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency) error
		// UpsertOne Upsert一条数据
		UpsertOne(ctx context.Context, data *channel_model.Agency) error
		// UpsertOneByTx Upsert一条数据(事务)
		UpsertOneByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency) error
		// UpsertOneByFields Upsert一条数据，根据fields字段
		UpsertOneByFields(ctx context.Context, data *channel_model.Agency, fields []string) error
		// UpsertOneByFieldsTx Upsert一条数据，根据fields字段(事务)
		UpsertOneByFieldsTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency, fields []string) error
		// CreateBatch 批量创建数据
		CreateBatch(ctx context.Context, data []*channel_model.Agency, batchSize int) error
		// UpdateOne 更新一条数据
		UpdateOne(ctx context.Context, data *channel_model.Agency) error
		// UpdateOne 更新一条数据(事务)
		UpdateOneByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency) error
		// UpdateOneWithZero 更新一条数据,包含零值
		UpdateOneWithZero(ctx context.Context, data *channel_model.Agency) error
		// UpdateOneWithZero 更新一条数据,包含零值(事务)
		UpdateOneWithZeroByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency) error
		// FindOneCacheByID 根据ID查询一条数据并设置缓存
		FindOneCacheByID(ctx context.Context, ID int64) (*channel_model.Agency, error)
		// FindOneByID 根据ID查询一条数据
		FindOneByID(ctx context.Context, ID int64) (*channel_model.Agency, error)
		// FindMultiCacheByIDS 根据IDS查询多条数据并设置缓存
		FindMultiCacheByIDS(ctx context.Context, IDS []int64) ([]*channel_model.Agency, error)
		// FindMultiByIDS 根据IDS查询多条数据
		FindMultiByIDS(ctx context.Context, IDS []int64) ([]*channel_model.Agency, error)
		// Deprecated
		// 请使用FindMultiByCondition替代
		FindMultiByPaginator(ctx context.Context, paginatorReq *orm.PaginatorReq) ([]*channel_model.Agency, *orm.PaginatorReply, error)
		// Deprecated
		// 请使用FindMultiByCondition替代
		FindMultiByCustom(ctx context.Context, customReq *custom.Req) ([]*channel_model.Agency, *custom.Reply, error)
		// FindMultiByCondition 根据自定义条件查询数据
		FindMultiByCondition(ctx context.Context, conditionReq *condition.Req) ([]*channel_model.Agency, *condition.Reply, error)
		// DeleteOneCacheByID 根据ID删除一条数据并清理缓存
		DeleteOneCacheByID(ctx context.Context, ID int64) error
		// DeleteOneCacheByID 根据ID删除一条数据并清理缓存
		DeleteOneCacheByIDTx(ctx context.Context, tx *channel_dao.Query, ID int64) error
		// DeleteOneByID 根据ID删除一条数据
		DeleteOneByID(ctx context.Context, ID int64) error
		// DeleteOneByID 根据ID删除一条数据
		DeleteOneByIDTx(ctx context.Context, tx *channel_dao.Query, ID int64) error
		// DeleteMultiCacheByIDS 根据IDS删除多条数据并清理缓存
		DeleteMultiCacheByIDS(ctx context.Context, IDS []int64) error
		// DeleteMultiCacheByIDS 根据IDS删除多条数据并清理缓存
		DeleteMultiCacheByIDSTx(ctx context.Context, tx *channel_dao.Query, IDS []int64) error
		// DeleteMultiByIDS 根据IDS删除多条数据
		DeleteMultiByIDS(ctx context.Context, IDS []int64) error
		// DeleteMultiByIDS 根据IDS删除多条数据
		DeleteMultiByIDSTx(ctx context.Context, tx *channel_dao.Query, IDS []int64) error
		// DeleteUniqueIndexCache 删除唯一索引存在的缓存
		DeleteUniqueIndexCache(ctx context.Context, data []*channel_model.Agency) error
	}
	AgencyRepo struct {
		db    *gorm.DB
		cache cache.IDBCache
	}
)

func NewAgencyRepo(db *gorm.DB, cache cache.IDBCache) *AgencyRepo {
	return &AgencyRepo{
		db:    db,
		cache: cache,
	}
}

// CreateOne 创建一条数据
func (a *AgencyRepo) CreateOne(ctx context.Context, data *channel_model.Agency) error {
	dao := channel_dao.Use(a.db).Agency
	err := dao.WithContext(ctx).Create(data)
	if err != nil {
		return err
	}
	return nil
}

// CreateOneByTx 创建一条数据(事务)
func (a *AgencyRepo) CreateOneByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency) error {
	dao := tx.Agency
	err := dao.WithContext(ctx).Create(data)
	if err != nil {
		return err
	}
	return nil
}

// UpsertOne Upsert一条数据
func (a *AgencyRepo) UpsertOne(ctx context.Context, data *channel_model.Agency) error {
	dao := channel_dao.Use(a.db).Agency
	err := dao.WithContext(ctx).Save(data)
	if err != nil {
		return err
	}
	return nil
}

// UpsertOneByTx Upsert一条数据(事务)
func (a *AgencyRepo) UpsertOneByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency) error {
	dao := tx.Agency
	err := dao.WithContext(ctx).Save(data)
	if err != nil {
		return err
	}
	return nil
}

// UpsertOneByFields Upsert一条数据，根据fields字段
func (a *AgencyRepo) UpsertOneByFields(ctx context.Context, data *channel_model.Agency, fields []string) error {
	if len(fields) == 0 {
		return errors.New("UpsertOneByFields fields is empty")
	}
	columns := make([]clause.Column, 0)
	for _, v := range fields {
		columns = append(columns, clause.Column{Name: v})
	}
	dao := channel_dao.Use(a.db).Agency
	err := dao.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   columns,
		UpdateAll: true,
	}).Create(data)
	if err != nil {
		return err
	}
	return nil
}

// UpsertOneByFieldsTx Upsert一条数据，根据fields字段(事务)
func (a *AgencyRepo) UpsertOneByFieldsTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency, fields []string) error {
	if len(fields) == 0 {
		return errors.New("UpsertOneByFieldsTx fields is empty")
	}
	columns := make([]clause.Column, 0)
	for _, v := range fields {
		columns = append(columns, clause.Column{Name: v})
	}
	dao := tx.Agency
	err := dao.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   columns,
		UpdateAll: true,
	}).Create(data)
	if err != nil {
		return err
	}
	return nil
}

// CreateBatch 批量创建数据
func (a *AgencyRepo) CreateBatch(ctx context.Context, data []*channel_model.Agency, batchSize int) error {
	dao := channel_dao.Use(a.db).Agency
	err := dao.WithContext(ctx).CreateInBatches(data, batchSize)
	if err != nil {
		return err
	}
	return nil
}

// UpdateOne 更新一条数据
func (a *AgencyRepo) UpdateOne(ctx context.Context, data *channel_model.Agency) error {
	// 对敏感字段进行加密，因为 Updates 不会触发 BeforeSave hook
	if err := data.EncryptSensitiveFields(); err != nil {
		return err
	}

	dao := channel_dao.Use(a.db).Agency
	_, err := dao.WithContext(ctx).Where(dao.ID.Eq(data.ID)).Updates(data)
	if err != nil {
		return err
	}
	err = a.DeleteUniqueIndexCache(ctx, []*channel_model.Agency{data})
	if err != nil {
		return err
	}
	return nil
}

// UpdateOneByTx 更新一条数据(事务)
func (a *AgencyRepo) UpdateOneByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency) error {
	dao := tx.Agency
	_, err := dao.WithContext(ctx).Where(dao.ID.Eq(data.ID)).Updates(data)
	if err != nil {
		return err
	}
	err = a.DeleteUniqueIndexCache(ctx, []*channel_model.Agency{data})
	if err != nil {
		return err
	}
	return err
}

// UpdateOneWithZero 更新一条数据,包含零值
func (a *AgencyRepo) UpdateOneWithZero(ctx context.Context, data *channel_model.Agency) error {
	dao := channel_dao.Use(a.db).Agency
	_, err := dao.WithContext(ctx).Where(dao.ID.Eq(data.ID)).Select(dao.ALL.WithTable("")).Updates(data)
	if err != nil {
		return err
	}
	err = a.DeleteUniqueIndexCache(ctx, []*channel_model.Agency{data})
	if err != nil {
		return err
	}
	return nil
}

// UpdateOneWithZeroByTx 更新一条数据(事务),包含零值
func (a *AgencyRepo) UpdateOneWithZeroByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency) error {
	dao := tx.Agency
	_, err := dao.WithContext(ctx).Where(dao.ID.Eq(data.ID)).Select(dao.ALL.WithTable("")).Updates(data)
	if err != nil {
		return err
	}
	err = a.DeleteUniqueIndexCache(ctx, []*channel_model.Agency{data})
	if err != nil {
		return err
	}
	return err
}

// DeleteOneCacheByID 根据ID删除一条数据并清理缓存
func (a *AgencyRepo) DeleteOneCacheByID(ctx context.Context, ID int64) error {
	dao := channel_dao.Use(a.db).Agency
	result, err := dao.WithContext(ctx).Where(dao.ID.Eq(ID)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if result == nil {
		return nil
	}
	_, err = dao.WithContext(ctx).Where(dao.ID.Eq(ID)).Delete()
	if err != nil {
		return err
	}
	err = a.DeleteUniqueIndexCache(ctx, []*channel_model.Agency{result})
	if err != nil {
		return err
	}
	return nil
}

// DeleteOneCacheByID 根据ID删除一条数据并清理缓存
func (a *AgencyRepo) DeleteOneCacheByIDTx(ctx context.Context, tx *channel_dao.Query, ID int64) error {
	dao := tx.Agency
	result, err := dao.WithContext(ctx).Where(dao.ID.Eq(ID)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if result == nil {
		return nil
	}
	_, err = dao.WithContext(ctx).Where(dao.ID.Eq(ID)).Delete()
	if err != nil {
		return err
	}
	err = a.DeleteUniqueIndexCache(ctx, []*channel_model.Agency{result})
	if err != nil {
		return err
	}
	return nil
}

// DeleteOneByID 根据ID删除一条数据
func (a *AgencyRepo) DeleteOneByID(ctx context.Context, ID int64) error {
	dao := channel_dao.Use(a.db).Agency
	_, err := dao.WithContext(ctx).Where(dao.ID.Eq(ID)).Delete()
	if err != nil {
		return err
	}
	return nil
}

// DeleteOneByID 根据ID删除一条数据
func (a *AgencyRepo) DeleteOneByIDTx(ctx context.Context, tx *channel_dao.Query, ID int64) error {
	dao := tx.Agency
	_, err := dao.WithContext(ctx).Where(dao.ID.Eq(ID)).Delete()
	if err != nil {
		return err
	}
	return nil
}

// DeleteMultiCacheByIDS 根据IDS删除多条数据并清理缓存
func (a *AgencyRepo) DeleteMultiCacheByIDS(ctx context.Context, IDS []int64) error {
	dao := channel_dao.Use(a.db).Agency
	result, err := dao.WithContext(ctx).Where(dao.ID.In(IDS...)).Find()
	if err != nil {
		return err
	}
	if len(result) == 0 {
		return nil
	}
	_, err = dao.WithContext(ctx).Where(dao.ID.In(IDS...)).Delete()
	if err != nil {
		return err
	}
	err = a.DeleteUniqueIndexCache(ctx, result)
	if err != nil {
		return err
	}
	return nil
}

// DeleteMultiCacheByIDS 根据IDS删除多条数据并清理缓存
func (a *AgencyRepo) DeleteMultiCacheByIDSTx(ctx context.Context, tx *channel_dao.Query, IDS []int64) error {
	dao := tx.Agency
	result, err := dao.WithContext(ctx).Where(dao.ID.In(IDS...)).Find()
	if err != nil {
		return err
	}
	if len(result) == 0 {
		return nil
	}
	_, err = dao.WithContext(ctx).Where(dao.ID.In(IDS...)).Delete()
	if err != nil {
		return err
	}
	err = a.DeleteUniqueIndexCache(ctx, result)
	if err != nil {
		return err
	}
	return nil
}

// DeleteMultiByIDS 根据IDS删除多条数据
func (a *AgencyRepo) DeleteMultiByIDS(ctx context.Context, IDS []int64) error {
	dao := channel_dao.Use(a.db).Agency
	_, err := dao.WithContext(ctx).Where(dao.ID.In(IDS...)).Delete()
	if err != nil {
		return err
	}
	return nil
}

// DeleteMultiByIDS 根据IDS删除多条数据
func (a *AgencyRepo) DeleteMultiByIDSTx(ctx context.Context, tx *channel_dao.Query, IDS []int64) error {
	dao := tx.Agency
	_, err := dao.WithContext(ctx).Where(dao.ID.In(IDS...)).Delete()
	if err != nil {
		return err
	}
	return nil
}

// DeleteUniqueIndexCache 删除唯一索引存在的缓存
func (a *AgencyRepo) DeleteUniqueIndexCache(ctx context.Context, data []*channel_model.Agency) error {
	keys := make([]string, 0)
	for _, v := range data {
		keys = append(keys, a.cache.Key(CacheAgencyByIDPrefix, v.ID))

	}
	err := a.cache.DelBatch(ctx, keys)
	if err != nil {
		return err
	}
	return nil
}

// FindOneCacheByID 根据ID查询一条数据并设置缓存
func (a *AgencyRepo) FindOneCacheByID(ctx context.Context, ID int64) (*channel_model.Agency, error) {
	resp := new(channel_model.Agency)
	cacheKey := a.cache.Key(CacheAgencyByIDPrefix, ID)
	cacheValue, err := a.cache.Fetch(ctx, cacheKey, func() (string, error) {
		dao := channel_dao.Use(a.db).Agency
		result, err := dao.WithContext(ctx).Where(dao.ID.Eq(ID)).First()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return "", err
		}
		marshal, err := json.Marshal(result)
		if err != nil {
			return "", err
		}
		return string(marshal), nil
	})
	if err != nil {
		return nil, err
	}
	if cacheValue != "" {
		err = json.Unmarshal([]byte(cacheValue), resp)
		if err != nil {
			return nil, err
		}
	}
	return resp, nil
}

// FindOneByID 根据ID查询一条数据
func (a *AgencyRepo) FindOneByID(ctx context.Context, ID int64) (*channel_model.Agency, error) {
	dao := channel_dao.Use(a.db).Agency
	result, err := dao.WithContext(ctx).Where(dao.ID.Eq(ID)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return result, nil
}

// FindMultiCacheByIDS 根据IDS查询多条数据并设置缓存
func (a *AgencyRepo) FindMultiCacheByIDS(ctx context.Context, IDS []int64) ([]*channel_model.Agency, error) {
	resp := make([]*channel_model.Agency, 0)
	cacheKeys := make([]string, 0)
	keyToParam := make(map[string]int64)
	for _, v := range IDS {
		cacheKey := a.cache.Key(CacheAgencyByIDPrefix, v)
		cacheKeys = append(cacheKeys, cacheKey)
		keyToParam[cacheKey] = v
	}
	cacheValue, err := a.cache.FetchBatch(ctx, cacheKeys, func(miss []string) (map[string]string, error) {
		parameters := make([]int64, 0)
		for _, v := range miss {
			parameters = append(parameters, keyToParam[v])
		}
		dao := channel_dao.Use(a.db).Agency
		result, err := dao.WithContext(ctx).Where(dao.ID.In(parameters...)).Find()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		value := make(map[string]string)
		for _, v := range miss {
			value[v] = ""
		}
		for _, v := range result {
			marshal, err := json.Marshal(v)
			if err != nil {
				return nil, err
			}
			value[a.cache.Key(CacheAgencyByIDPrefix, v.ID)] = string(marshal)
		}
		return value, nil
	})
	if err != nil {
		return nil, err
	}
	for _, v := range cacheValue {
		tmp := new(channel_model.Agency)
		if v != "" {
			err := json.Unmarshal([]byte(v), tmp)
			if err != nil {
				return nil, err
			}
		}
		resp = append(resp, tmp)
	}
	return resp, nil
}

// FindMultiByIDS 根据IDS查询多条数据
func (a *AgencyRepo) FindMultiByIDS(ctx context.Context, IDS []int64) ([]*channel_model.Agency, error) {
	dao := channel_dao.Use(a.db).Agency
	result, err := dao.WithContext(ctx).Where(dao.ID.In(IDS...)).Find()
	if err != nil {
		return nil, err
	}
	return result, nil
}

// Deprecated
// 请使用FindMultiByCondition替代
func (a *AgencyRepo) FindMultiByPaginator(ctx context.Context, paginatorReq *orm.PaginatorReq) ([]*channel_model.Agency, *orm.PaginatorReply, error) {
	result := make([]*channel_model.Agency, 0)
	var total int64
	whereExpressions, orderExpressions, err := paginatorReq.ConvertToGormExpression(channel_model.Agency{})
	if err != nil {
		return result, nil, err
	}
	err = a.db.WithContext(ctx).Model(&channel_model.Agency{}).Select([]string{"*"}).Clauses(whereExpressions...).Count(&total).Error
	if err != nil {
		return result, nil, err
	}
	if total == 0 {
		return result, nil, nil
	}
	paginatorReply := paginatorReq.ConvertToPage(int(total))
	err = a.db.WithContext(ctx).Model(&channel_model.Agency{}).Limit(paginatorReply.Limit).Offset(paginatorReply.Offset).Clauses(whereExpressions...).Clauses(orderExpressions...).Find(&result).Error
	if err != nil {
		return result, nil, err
	}
	return result, paginatorReply, err
}

// Deprecated
// 请使用FindMultiByCondition替代
func (a *AgencyRepo) FindMultiByCustom(ctx context.Context, customReq *custom.Req) ([]*channel_model.Agency, *custom.Reply, error) {
	result := make([]*channel_model.Agency, 0)
	var total int64
	whereExpressions, orderExpressions, err := customReq.ConvertToGormExpression(channel_model.Agency{})
	if err != nil {
		return result, nil, err
	}
	err = a.db.WithContext(ctx).Model(&channel_model.Agency{}).Select([]string{"*"}).Clauses(whereExpressions...).Count(&total).Error
	if err != nil {
		return result, nil, err
	}
	if total == 0 {
		return result, nil, nil
	}
	customReply, err := customReq.ConvertToPage(int(total))
	if err != nil {
		return result, nil, err
	}
	query := a.db.WithContext(ctx).Model(&channel_model.Agency{}).Clauses(whereExpressions...).Clauses(orderExpressions...)
	if customReply.Offset != 0 {
		query = query.Offset(customReply.Offset)
	}
	if customReply.Limit != 0 {
		query = query.Limit(customReply.Limit)
	}
	err = query.Find(&result).Error
	if err != nil {
		return result, nil, err
	}
	return result, customReply, err
}

// FindMultiByCondition 自定义查询数据(通用)
func (a *AgencyRepo) FindMultiByCondition(ctx context.Context, conditionReq *condition.Req) ([]*channel_model.Agency, *condition.Reply, error) {
	result := make([]*channel_model.Agency, 0)
	var total int64
	whereExpressions, orderExpressions, err := conditionReq.ConvertToGormExpression(channel_model.Agency{})
	if err != nil {
		return result, nil, err
	}
	err = a.db.WithContext(ctx).Model(&channel_model.Agency{}).Select([]string{"*"}).Clauses(whereExpressions...).Count(&total).Error
	if err != nil {
		return result, nil, err
	}
	if total == 0 {
		return result, nil, nil
	}
	conditionReply, err := conditionReq.ConvertToPage(int32(total))
	if err != nil {
		return result, nil, err
	}
	query := a.db.WithContext(ctx).Model(&channel_model.Agency{}).Clauses(whereExpressions...).Clauses(orderExpressions...)
	if conditionReply.Page != 0 && conditionReply.PageSize != 0 {
		query = query.Offset(int((conditionReply.Page - 1) * conditionReply.PageSize))
		query = query.Limit(int(conditionReply.PageSize))
	}
	err = query.Find(&result).Error
	if err != nil {
		return result, nil, err
	}
	return result, conditionReply, err
}
