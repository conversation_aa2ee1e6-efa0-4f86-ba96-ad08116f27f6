// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package channel_dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_model"
)

func newAgency(db *gorm.DB, opts ...gen.DOOption) agency {
	_agency := agency{}

	_agency.agencyDo.UseDB(db, opts...)
	_agency.agencyDo.UseModel(&channel_model.Agency{})

	tableName := _agency.agencyDo.TableName()
	_agency.ALL = field.NewAsterisk(tableName)
	_agency.ID = field.NewInt64(tableName, "id")
	_agency.Name = field.NewString(tableName, "name")
	_agency.ChargeName = field.NewString(tableName, "charge_name")
	_agency.ChargePhone = field.NewString(tableName, "charge_phone")
	_agency.RegionType = field.NewInt32(tableName, "region_type")
	_agency.Level = field.NewInt32(tableName, "level")
	_agency.StageSubject = field.NewField(tableName, "stage_subject")
	_agency.CreatedDate = field.NewTime(tableName, "created_date")
	_agency.CreateUserID = field.NewString(tableName, "create_user_id")
	_agency.UpdatedDate = field.NewField(tableName, "updated_date")
	_agency.UpdateUserID = field.NewString(tableName, "update_user_id")
	_agency.Deleted = field.NewBool(tableName, "deleted")
	_agency.Status = field.NewBool(tableName, "status")
	_agency.EnterpriseName = field.NewString(tableName, "enterprise_name")
	_agency.EnterpriseType = field.NewString(tableName, "enterprise_type")
	_agency.EnterpriseLegalPerson = field.NewString(tableName, "enterprise_legal_person")
	_agency.EnterpriseAddress = field.NewString(tableName, "enterprise_address")
	_agency.TaxpayerIdentificationNumber = field.NewString(tableName, "taxpayer_identification_number")
	_agency.InvoiceTitle = field.NewString(tableName, "invoice_title")
	_agency.BusinessLicense = field.NewString(tableName, "business_license")
	_agency.TaxpayerQualification = field.NewString(tableName, "taxpayer_qualification")
	_agency.Remark = field.NewString(tableName, "remark")
	_agency.IDPhoto = field.NewString(tableName, "ID_Photo")
	_agency.CustomerPhone = field.NewString(tableName, "customer_phone")
	_agency.OldName = field.NewString(tableName, "old_name")
	_agency.FinancePhone = field.NewString(tableName, "finance_phone")
	_agency.BusinessPhone = field.NewString(tableName, "business_phone")
	_agency.SchoolReserveCount = field.NewInt32(tableName, "school_reserve_count")
	_agency.OnionEmployeeCount = field.NewInt32(tableName, "onion_employee_count")
	_agency.GuestDesc = field.NewString(tableName, "guest_desc")
	_agency.OtherProduct = field.NewString(tableName, "other_product")
	_agency.TeamDesc = field.NewString(tableName, "team_desc")
	_agency.CooperationFeeFile = field.NewString(tableName, "cooperation_fee_file")
	_agency.ReceiverAddress = field.NewString(tableName, "receiver_address")
	_agency.SignedMan = field.NewString(tableName, "signed_man")
	_agency.SignedManPhone = field.NewString(tableName, "signed_man_phone")
	_agency.PrepayImg = field.NewString(tableName, "prepay_img")
	_agency.BaseInfo = field.NewString(tableName, "base_info")
	_agency.AgencyLevel = field.NewString(tableName, "agency_level")
	_agency.AgencyType = field.NewString(tableName, "agency_type")
	_agency.Eml = field.NewString(tableName, "eml")
	_agency.FeishuVendorID = field.NewString(tableName, "feishu_vendor_id")
	_agency.FeishuVendor = field.NewString(tableName, "feishu_vendor")
	_agency.TaxpayersType = field.NewString(tableName, "taxpayers_type")
	_agency.SignedManPh = field.NewString(tableName, "signed_man_ph")
	_agency.CustomerPh = field.NewString(tableName, "customer_ph")

	_agency.fillFieldMap()

	return _agency
}

type agency struct {
	agencyDo agencyDo

	ALL                          field.Asterisk
	ID                           field.Int64  // 代理商ID
	Name                         field.String // 代理商名称
	ChargeName                   field.String // 负责人名称
	ChargePhone                  field.String // 负责人电话
	RegionType                   field.Int32  // 代理商类型
	Level                        field.Int32  // 代理商级别（旧，已废弃，后续删除）
	StageSubject                 field.Field  // 学科学段
	CreatedDate                  field.Time   // 创建时间
	CreateUserID                 field.String // 创建人
	UpdatedDate                  field.Field  // 代理商更新时间
	UpdateUserID                 field.String // 最近更改人
	Deleted                      field.Bool   // 是否被删除
	Status                       field.Bool   // 活跃状态
	EnterpriseName               field.String // 企业名称
	EnterpriseType               field.String // 企业类型
	EnterpriseLegalPerson        field.String // 企业法人
	EnterpriseAddress            field.String // 企业地址
	TaxpayerIdentificationNumber field.String // 纳税人识别号
	InvoiceTitle                 field.String // 发票抬头
	BusinessLicense              field.String // 营业执照
	TaxpayerQualification        field.String // 纳税人资质证明
	Remark                       field.String // 备注
	IDPhoto                      field.String
	CustomerPhone                field.String // 客服电话
	OldName                      field.String // 代理商曾用名
	FinancePhone                 field.String // 财务对接人电话
	BusinessPhone                field.String // 业务对接人电话
	SchoolReserveCount           field.Int32  // 学校储备数量
	OnionEmployeeCount           field.Int32  // 负责洋葱员工数量
	GuestDesc                    field.String // 客情关系描述
	OtherProduct                 field.String // 代理其他产品情况
	TeamDesc                     field.String // 团队建设情况
	CooperationFeeFile           field.String // 合作费截图
	ReceiverAddress              field.String // 收件人地址
	SignedMan                    field.String // 代理签约人姓名
	SignedManPhone               field.String // 代理签约人电话
	PrepayImg                    field.String // 预付款截图
	BaseInfo                     field.String // 代理商基本信息
	AgencyLevel                  field.String // 代理商级别（新）
	AgencyType                   field.String // 代理商类型，agent：合作代理商、direct：直营代理商
	Eml                          field.String // 邮箱
	FeishuVendorID               field.String // 飞书交易方id
	FeishuVendor                 field.String // 飞书交易方编码
	TaxpayersType                field.String // 纳税人类型
	SignedManPh                  field.String // 代理签约人电话
	CustomerPh                   field.String // 客服电话

	fieldMap map[string]field.Expr
}

func (a agency) Table(newTableName string) *agency {
	a.agencyDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a agency) As(alias string) *agency {
	a.agencyDo.DO = *(a.agencyDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *agency) updateTableName(table string) *agency {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.Name = field.NewString(table, "name")
	a.ChargeName = field.NewString(table, "charge_name")
	a.ChargePhone = field.NewString(table, "charge_phone")
	a.RegionType = field.NewInt32(table, "region_type")
	a.Level = field.NewInt32(table, "level")
	a.StageSubject = field.NewField(table, "stage_subject")
	a.CreatedDate = field.NewTime(table, "created_date")
	a.CreateUserID = field.NewString(table, "create_user_id")
	a.UpdatedDate = field.NewField(table, "updated_date")
	a.UpdateUserID = field.NewString(table, "update_user_id")
	a.Deleted = field.NewBool(table, "deleted")
	a.Status = field.NewBool(table, "status")
	a.EnterpriseName = field.NewString(table, "enterprise_name")
	a.EnterpriseType = field.NewString(table, "enterprise_type")
	a.EnterpriseLegalPerson = field.NewString(table, "enterprise_legal_person")
	a.EnterpriseAddress = field.NewString(table, "enterprise_address")
	a.TaxpayerIdentificationNumber = field.NewString(table, "taxpayer_identification_number")
	a.InvoiceTitle = field.NewString(table, "invoice_title")
	a.BusinessLicense = field.NewString(table, "business_license")
	a.TaxpayerQualification = field.NewString(table, "taxpayer_qualification")
	a.Remark = field.NewString(table, "remark")
	a.IDPhoto = field.NewString(table, "ID_Photo")
	a.CustomerPhone = field.NewString(table, "customer_phone")
	a.OldName = field.NewString(table, "old_name")
	a.FinancePhone = field.NewString(table, "finance_phone")
	a.BusinessPhone = field.NewString(table, "business_phone")
	a.SchoolReserveCount = field.NewInt32(table, "school_reserve_count")
	a.OnionEmployeeCount = field.NewInt32(table, "onion_employee_count")
	a.GuestDesc = field.NewString(table, "guest_desc")
	a.OtherProduct = field.NewString(table, "other_product")
	a.TeamDesc = field.NewString(table, "team_desc")
	a.CooperationFeeFile = field.NewString(table, "cooperation_fee_file")
	a.ReceiverAddress = field.NewString(table, "receiver_address")
	a.SignedMan = field.NewString(table, "signed_man")
	a.SignedManPhone = field.NewString(table, "signed_man_phone")
	a.PrepayImg = field.NewString(table, "prepay_img")
	a.BaseInfo = field.NewString(table, "base_info")
	a.AgencyLevel = field.NewString(table, "agency_level")
	a.AgencyType = field.NewString(table, "agency_type")
	a.Eml = field.NewString(table, "eml")
	a.FeishuVendorID = field.NewString(table, "feishu_vendor_id")
	a.FeishuVendor = field.NewString(table, "feishu_vendor")
	a.TaxpayersType = field.NewString(table, "taxpayers_type")
	a.SignedManPh = field.NewString(table, "signed_man_ph")
	a.CustomerPh = field.NewString(table, "customer_ph")

	a.fillFieldMap()

	return a
}

func (a *agency) WithContext(ctx context.Context) *agencyDo { return a.agencyDo.WithContext(ctx) }

func (a agency) TableName() string { return a.agencyDo.TableName() }

func (a agency) Alias() string { return a.agencyDo.Alias() }

func (a agency) Columns(cols ...field.Expr) gen.Columns { return a.agencyDo.Columns(cols...) }

func (a *agency) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *agency) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 44)
	a.fieldMap["id"] = a.ID
	a.fieldMap["name"] = a.Name
	a.fieldMap["charge_name"] = a.ChargeName
	a.fieldMap["charge_phone"] = a.ChargePhone
	a.fieldMap["region_type"] = a.RegionType
	a.fieldMap["level"] = a.Level
	a.fieldMap["stage_subject"] = a.StageSubject
	a.fieldMap["created_date"] = a.CreatedDate
	a.fieldMap["create_user_id"] = a.CreateUserID
	a.fieldMap["updated_date"] = a.UpdatedDate
	a.fieldMap["update_user_id"] = a.UpdateUserID
	a.fieldMap["deleted"] = a.Deleted
	a.fieldMap["status"] = a.Status
	a.fieldMap["enterprise_name"] = a.EnterpriseName
	a.fieldMap["enterprise_type"] = a.EnterpriseType
	a.fieldMap["enterprise_legal_person"] = a.EnterpriseLegalPerson
	a.fieldMap["enterprise_address"] = a.EnterpriseAddress
	a.fieldMap["taxpayer_identification_number"] = a.TaxpayerIdentificationNumber
	a.fieldMap["invoice_title"] = a.InvoiceTitle
	a.fieldMap["business_license"] = a.BusinessLicense
	a.fieldMap["taxpayer_qualification"] = a.TaxpayerQualification
	a.fieldMap["remark"] = a.Remark
	a.fieldMap["ID_Photo"] = a.IDPhoto
	a.fieldMap["customer_phone"] = a.CustomerPhone
	a.fieldMap["old_name"] = a.OldName
	a.fieldMap["finance_phone"] = a.FinancePhone
	a.fieldMap["business_phone"] = a.BusinessPhone
	a.fieldMap["school_reserve_count"] = a.SchoolReserveCount
	a.fieldMap["onion_employee_count"] = a.OnionEmployeeCount
	a.fieldMap["guest_desc"] = a.GuestDesc
	a.fieldMap["other_product"] = a.OtherProduct
	a.fieldMap["team_desc"] = a.TeamDesc
	a.fieldMap["cooperation_fee_file"] = a.CooperationFeeFile
	a.fieldMap["receiver_address"] = a.ReceiverAddress
	a.fieldMap["signed_man"] = a.SignedMan
	a.fieldMap["signed_man_phone"] = a.SignedManPhone
	a.fieldMap["prepay_img"] = a.PrepayImg
	a.fieldMap["base_info"] = a.BaseInfo
	a.fieldMap["agency_level"] = a.AgencyLevel
	a.fieldMap["agency_type"] = a.AgencyType
	a.fieldMap["eml"] = a.Eml
	a.fieldMap["feishu_vendor_id"] = a.FeishuVendorID
	a.fieldMap["feishu_vendor"] = a.FeishuVendor
	a.fieldMap["taxpayers_type"] = a.TaxpayersType
}

func (a agency) clone(db *gorm.DB) agency {
	a.agencyDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a agency) replaceDB(db *gorm.DB) agency {
	a.agencyDo.ReplaceDB(db)
	return a
}

type agencyDo struct{ gen.DO }

func (a agencyDo) Debug() *agencyDo {
	return a.withDO(a.DO.Debug())
}

func (a agencyDo) WithContext(ctx context.Context) *agencyDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a agencyDo) ReadDB() *agencyDo {
	return a.Clauses(dbresolver.Read)
}

func (a agencyDo) WriteDB() *agencyDo {
	return a.Clauses(dbresolver.Write)
}

func (a agencyDo) Session(config *gorm.Session) *agencyDo {
	return a.withDO(a.DO.Session(config))
}

func (a agencyDo) Clauses(conds ...clause.Expression) *agencyDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a agencyDo) Returning(value interface{}, columns ...string) *agencyDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a agencyDo) Not(conds ...gen.Condition) *agencyDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a agencyDo) Or(conds ...gen.Condition) *agencyDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a agencyDo) Select(conds ...field.Expr) *agencyDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a agencyDo) Where(conds ...gen.Condition) *agencyDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a agencyDo) Order(conds ...field.Expr) *agencyDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a agencyDo) Distinct(cols ...field.Expr) *agencyDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a agencyDo) Omit(cols ...field.Expr) *agencyDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a agencyDo) Join(table schema.Tabler, on ...field.Expr) *agencyDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a agencyDo) LeftJoin(table schema.Tabler, on ...field.Expr) *agencyDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a agencyDo) RightJoin(table schema.Tabler, on ...field.Expr) *agencyDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a agencyDo) Group(cols ...field.Expr) *agencyDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a agencyDo) Having(conds ...gen.Condition) *agencyDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a agencyDo) Limit(limit int) *agencyDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a agencyDo) Offset(offset int) *agencyDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a agencyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *agencyDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a agencyDo) Unscoped() *agencyDo {
	return a.withDO(a.DO.Unscoped())
}

func (a agencyDo) Create(values ...*channel_model.Agency) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a agencyDo) CreateInBatches(values []*channel_model.Agency, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a agencyDo) Save(values ...*channel_model.Agency) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a agencyDo) First() (*channel_model.Agency, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*channel_model.Agency), nil
	}
}

func (a agencyDo) Take() (*channel_model.Agency, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*channel_model.Agency), nil
	}
}

func (a agencyDo) Last() (*channel_model.Agency, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*channel_model.Agency), nil
	}
}

func (a agencyDo) Find() ([]*channel_model.Agency, error) {
	result, err := a.DO.Find()
	return result.([]*channel_model.Agency), err
}

func (a agencyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*channel_model.Agency, err error) {
	buf := make([]*channel_model.Agency, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a agencyDo) FindInBatches(result *[]*channel_model.Agency, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a agencyDo) Attrs(attrs ...field.AssignExpr) *agencyDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a agencyDo) Assign(attrs ...field.AssignExpr) *agencyDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a agencyDo) Joins(fields ...field.RelationField) *agencyDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a agencyDo) Preload(fields ...field.RelationField) *agencyDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a agencyDo) FirstOrInit() (*channel_model.Agency, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*channel_model.Agency), nil
	}
}

func (a agencyDo) FirstOrCreate() (*channel_model.Agency, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*channel_model.Agency), nil
	}
}

func (a agencyDo) FindByPage(offset int, limit int) (result []*channel_model.Agency, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a agencyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a agencyDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a agencyDo) Delete(models ...*channel_model.Agency) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *agencyDo) withDO(do gen.Dao) *agencyDo {
	a.DO = *do.(*gen.DO)
	return a
}
