package data

import (
	"context"
	"encoding/base64"
	"time"

	"errors"

	"fmt"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	domainmodel "gitlab.yc345.tv/backend/channel/internal/biz/domain_model"
	"gitlab.yc345.tv/backend/channel/internal/biz/entity"
	"gitlab.yc345.tv/backend/channel/internal/biz/iface"
	"gitlab.yc345.tv/backend/channel/internal/biz/usercore"
	"gitlab.yc345.tv/backend/channel/internal/common"
	"gitlab.yc345.tv/backend/channel/internal/common/regionhelper"
	"gitlab.yc345.tv/backend/channel/internal/conf"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_dao"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_model"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_repo"
	"gitlab.yc345.tv/backend/channel/internal/data/model"
	"gitlab.yc345.tv/backend/utils/v2/client/redis"
	"gorm.io/gorm"
)

type agencies struct {
	channel_repo.IAgencyRepo
	cache  redis.IClient
	log    *log.Helper
	config *conf.Bootstrap
	data   *Data
}

func NewAgenciesRepo(cache redis.IClient, data *Data, logger log.Logger, config *conf.Bootstrap,
	repo *channel_repo.AgencyRepo,
) iface.IAgencyRepo {
	return &agencies{
		IAgencyRepo: repo,
		cache:       cache,
		log:         log.NewHelper(logger),
		config:      config,
		data:        data,
	}
}

func NewAgencyInfoRepo() usercore.AgencyInfoRepo {
	return &agencies{}
}
func (a *agencies) GetAgencyByIDs(ctx context.Context, agencyIDs []string) ([]channel_model.Agency, error) {
	res := make([]channel_model.Agency, 0)
	err := a.data.DB().WithContext(ctx).Where("id in ?", agencyIDs).Find(&res).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return res, err
	}
	// 解密敏感字段
	if err := DecryptAgenciesValueSensitiveFields(res); err != nil {
		a.log.Error("DecryptAgenciesValueSensitiveFields error:", err)
	}
	return res, err
}
func (a *agencies) GetAgency(ctx context.Context, agencyID string) (channel_model.Agency, error) {
	agency := channel_model.Agency{}
	err := a.data.DB().WithContext(ctx).Where("id = ?", agencyID).First(&agency).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return agency, err
	}
	// 解密敏感字段
	if err := DecryptAgencySensitiveFields(&agency); err != nil {
		a.log.Error("DecryptAgencySensitiveFields error:", err)
	}
	return agency, err
}

func (a *agencies) GetAgencyRegion(ctx context.Context, agencyID string) (channel_model.AgencyContractRegion, error) {
	agencyRegion := channel_model.AgencyContractRegion{}
	now := time.Now().Format(common.DateTime)
	err := a.data.DB().WithContext(ctx).Model(model.AgencyContract{}).Select("acr.*").
		Joins(`left join "agencyContractRegion" as acr on "agencyContracts".id = acr.contract_id`).
		Where("agency_id = ?", agencyID).
		Where("is_abandoned = false").
		Where("is_stop = false").
		Where("start_date <= ?", now).
		Where("end_date > ?", now).
		Where("deleted_date is null").
		First(&agencyRegion).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return agencyRegion, err
	}
	return agencyRegion, err
}

func (a *agencies) GetAgencySuperVise(ctx context.Context, regionCode string) (model.Regions, error) {
	region := model.Regions{}
	err := a.data.DB().WithContext(ctx).Model(model.Regions{}).
		Where("region like ?", regionCode[:2]+"%").
		Where(`"group" = ?`, common.RoleSuperVise).
		First(&region).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return region, err
	}
	return region, err
}

// GetAgencyInfoBySchoolId implements usercore.AgencyInfoRepo.
func (a *agencies) GetAgencyInfoBySchoolId(ctx context.Context, schoolid int64) (*domainmodel.AgencyInfo, error) {
	var (
		agencySchool model.AgencyAreaSchool
	)
	if err := a.data.DB().WithContext(ctx).Model(model.AgencyAreaSchool{}).Where("school_id = ?", schoolid).Find(&agencySchool).Error; err != nil {
		return nil, err
	}
	if agencySchool.ID == "" {

	}
	return &domainmodel.AgencyInfo{
		AgencyName:      "test",
		AgencyId:        0,
		AgencyManagerId: "1",
		AgencyManager:   "test",
		SuperViseId:     "test",
		SuperVise:       "test",
	}, nil
}

func (a *agencies) GetSchoolEmployees(ctx context.Context, schoolIds []string, agencyIds []int64) ([]*channel_model.AgencyEmployeeSchool, error) {
	res := make([]*channel_model.AgencyEmployeeSchool, 0)
	tx := a.data.db.WithContext(ctx).Preload("User")

	if len(schoolIds) > 0 {
		tx = tx.Where("school_id in ?", schoolIds)
	}
	if len(agencyIds) > 0 {
		tx = tx.Where("agency_id in ?", agencyIds)
	}
	tx = tx.Where("effect_time_range @> ?::timestamptz", time.Now().Format(common.DateTime))
	err := tx.Find(&res).Error
	return res, err
}

func (a *agencies) GetAgenciesByIDs(ctx context.Context, agencyIDs []int64) ([]*channel_model.Agency, error) {
	res := make([]*channel_model.Agency, 0)
	if len(agencyIDs) == 0 {
		return res, nil
	}
	agency := channel_dao.Use(a.data.DB()).Agency
	return agency.WithContext(ctx).Where(agency.ID.In(agencyIDs...)).Where(agency.Deleted.Is(false)).Find()
}

func (a *agencies) GetAgenciesByOptions(ctx context.Context, opt *entity.GetAgenciesByOptions) (int64, []*entity.AgencyWithAdminUser, error) {
	res := make([]*entity.AgencyWithAdminUser, 0)
	var count int64
	agencyDB := a.data.DB().WithContext(ctx).Model(&channel_model.Agency{})
	agencyDao := channel_dao.Use(a.data.DB()).Agency
	simpleDB := a.data.DB().WithContext(ctx).Model(&channel_model.ContractSimple{})
	agencyDB.Where(agencyDao.Deleted.Is(false))
	if opt.AgentId > 0 {
		agencyDB = agencyDB.Where(agencyDao.ID.Eq(opt.AgentId))
	}
	withoutIDs := lo.WithoutEmpty(opt.WithoutIDs)
	if len(withoutIDs) > 0 {
		agencyDB = agencyDB.Where(agencyDao.ID.In(withoutIDs...))
	}
	if opt.AgentName != "" {
		agencyDB = agencyDB.Where(agencyDao.Name.Like("%" + opt.AgentName + "%"))
	}
	if opt.AgencyLevel != "" {
		agencyDB = agencyDB.Where(agencyDao.AgencyLevel.Eq(opt.AgencyLevel))
	}
	if opt.AgencyType != "" {
		agencyDB = agencyDB.Where(agencyDao.AgencyType.Eq(opt.AgencyType))
	}
	if opt.Status == "will" {
		simpleDB = simpleDB.Where("state = ?", "will")
	} else if opt.Status == "ing" {
		simpleDB = simpleDB.Where("state = ?", "ing")
	} else if opt.Status == "done" {
		simpleDB = simpleDB.Where("state = ?", "done")
	} else if opt.Status == "abandoned" {
		simpleDB = simpleDB.Where("state = ?", "abandoned")
	} else if opt.Status == "none" {
		// TODO: 需要验证
		agencyDB = agencyDB.Where(`id not in (select agency_id from "contractSimples")`)
	} else if opt.Status == "notAbandoned" {
		simpleDB = simpleDB.Where("state != ?", "abandoned")
	}
	if opt.AgentId > 0 {
		simpleDB = simpleDB.Where("agency_id = ?", opt.AgentId)
	}
	needFilterAgencyIDs := opt.Status != "" && opt.Status != "none"
	if opt.Type != "" {
		needFilterAgencyIDs = true
		arr := strings.Split(opt.Type, "-")
		simpleDB = simpleDB.Where("region_type = ?", arr[0])
		if len(arr) > 1 {
			simpleDB = simpleDB.Where("level = ?", arr[1])
		}
	}
	var viewList []*channel_model.ContractSimple
	err := simpleDB.Find(&viewList).Error
	if err != nil {
		return count, res, err
	}
	if len(opt.Regions) > 0 {
		viewList = lo.Filter(viewList, func(c *channel_model.ContractSimple, _ int) bool {
			for i := 0; i < len(opt.Regions); i++ {
				re := opt.Regions[i]
				if regionhelper.IsProvince(re) {
					// 省级
					for j := 0; j < len(c.Regions); j++ {
						r := c.Regions[j]
						if len(r) > 2 && len(re) > 2 {
							if r[:2] == re[:2] {
								return true
							}
						}
					}
				} else if regionhelper.IsCity(re) {
					// 市级
					for j := 0; j < len(c.Regions); j++ {
						r := c.Regions[j]
						if len(r) > 4 && len(re) > 4 {
							if r[:4] == re[:4] || r == fmt.Sprintf("%s0000", re[:2]) {
								return true
							}
						}
					}
				} else {
					// 区级
					for j := 0; j < len(c.Regions); j++ {
						r := c.Regions[j]
						if len(r) > 4 && len(re) > 4 {
							if r == re || r == fmt.Sprintf("%s0000", re[:2]) || r == fmt.Sprintf("%s00", re[:4]) {
								return true
							}
						}
					}
				}

			}
			return false
		})
		needFilterAgencyIDs = true
	}
	if len(viewList) == 0 {
		return count, res, nil
	}
	if needFilterAgencyIDs {
		agencyIDs := lo.Uniq(lo.Map(viewList, func(item *channel_model.ContractSimple, _ int) int64 {
			return int64(item.AgencyID)
		}))
		if opt.AgentId > 0 {
			if !lo.Contains(agencyIDs, opt.AgentId) {
				// 查询的代理商无合同
				return count, res, nil
			}
		} else {
			agencyDB = agencyDB.Where(agencyDao.ID.In(agencyIDs...))
		}
	}
	if opt.AdminPhone != "" {
		// 按代理商超管的手机号筛选
		// 对参数进行base64编码
		phone := base64.StdEncoding.EncodeToString([]byte(opt.AdminPhone))
		var agencyAdminUsers []*channel_model.User
		err := a.data.DB().WithContext(ctx).Model(&channel_model.User{}).
			Where("phone = ?", phone).
			Where("deleted_at is null").
			Where("role = 'agency'").
			Find(&agencyAdminUsers).Error
		if err != nil {
			return count, res, err
		}
		agencyIDs := lo.Uniq(lo.Map(agencyAdminUsers, func(item *channel_model.User, _ int) int64 {
			return int64(item.AgencyID)
		}))
		agencyDB = agencyDB.Where(agencyDao.ID.In(agencyIDs...))
	}
	err = agencyDB.Count(&count).Error
	if err != nil {
		return count, res, err
	}
	agencyDB = agencyDB.Order(fmt.Sprintf("%s desc", agencyDao.CreatedDate.ColumnName().String()))
	if opt.Offset != 0 {
		agencyDB = agencyDB.Offset(opt.Offset)
	}
	if opt.Limit != "" && opt.Limit != "all" {
		agencyDB = agencyDB.Limit(cast.ToInt(opt.Limit))
	}
	var agencies []*channel_model.Agency
	err = agencyDB.Find(&agencies).Error
	if err != nil {
		return count, res, err
	}
	// 解密敏感字段
	if err := DecryptAgenciesSensitiveFields(agencies); err != nil {
		a.log.Error("DecryptAgenciesSensitiveFields error:", err)
	}
	if len(agencies) == 0 {
		return count, res, nil
	}
	agencyIDs := lo.Uniq(lo.Map(agencies, func(item *channel_model.Agency, _ int) int64 {
		return item.ID
	}))
	var adminUsers []*channel_model.User
	agencyDB = a.data.DB().WithContext(ctx).Model(&channel_model.User{}).Where("deleted_at is null").Where("role = 'agency'").Where("agency_id in (?)", agencyIDs)
	err = agencyDB.Find(&adminUsers).Error
	if err != nil {
		return count, res, err
	}
	adminUserMap := lo.SliceToMap(adminUsers, func(item *channel_model.User) (int64, *channel_model.User) {
		if item.Phone != "" {
			phone, err := common.DecodeBase64(item.Phone)
			if err != nil {
				a.log.Error(err)
			}
			item.Phone = phone
		}
		return item.AgencyID, item
	})
	agencyViewMap := make(map[int32][]*entity.ContractViewState)
	for i := 0; i < len(viewList); i++ {
		view := viewList[i]
		agencyViewMap[view.AgencyID] = append(agencyViewMap[view.AgencyID], &entity.ContractViewState{
			State: view.State,
		})
	}
	for _, agency := range agencies {
		chargePhone := ""
		if agency.ChargePhone != "" {
			phone, err := common.DecodeBase64(agency.ChargePhone)
			if err != nil {
				a.log.Error(err)
			}
			chargePhone = phone
		}
		res = append(res, &entity.AgencyWithAdminUser{
			ID:               agency.ID,
			Name:             agency.Name,
			ChargeName:       agency.ChargeName,
			ChargePhone:      chargePhone,
			CreatedDate:      agency.CreatedDate,
			AgencyLevel:      agency.AgencyLevel,
			AgencyType:       agency.AgencyType,
			AgencyAdminPhone: adminUserMap[agency.ID],
			Views:            agencyViewMap[int32(agency.ID)],
			Status:           agency.Status,
		})
	}
	return count, res, nil
}
