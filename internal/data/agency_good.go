package data

import (
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"reflect"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gitlab.yc345.tv/backend/channel/internal/biz/entity"
	"gitlab.yc345.tv/backend/channel/internal/biz/iface"
	"gitlab.yc345.tv/backend/channel/internal/common"
	"gitlab.yc345.tv/backend/channel/internal/conf"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_dao"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_model"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_repo"
	"gitlab.yc345.tv/backend/utils/v2/client/redis"
)

type agencyGoodRepo struct {
	cache  redis.IClient
	log    *log.Helper
	config *conf.Bootstrap
	data   *Data
	channel_repo.IAgencyGoodRepo
}

func NewAgencyGoodRepo(cache redis.IClient, data *Data, logger log.Logger, config *conf.Bootstrap, repo *channel_repo.AgencyGoodRepo) iface.IAgencyGoodRepo {
	return &agencyGoodRepo{
		cache:           cache,
		log:             log.NewHelper(logger),
		config:          config,
		data:            data,
		IAgencyGoodRepo: repo,
	}
}

func (a *agencyGoodRepo) GetPaymentGoodsList(ctx context.Context, p entity.PaymentGoodsListParams) (int, []*channel_model.AgencyGood, error) {
	// 为兼容代付流程，默认为代付
	if p.GoodsGroup == "" {
		p.GoodsGroup = common.GoodsGroupPrepay
	}
	query := channel_dao.Use(a.data.DB()).AgencyGood
	db := a.data.DB().WithContext(ctx).Model(&channel_model.AgencyGood{})
	if len(p.GoodsID) > 0 {
		db = db.Where(query.GoodsID.In(p.GoodsID...))
	}
	if p.GoodsType != "" {
		db = db.Where(query.GoodsType.Eq(p.GoodsType))
	}
	if p.GoodsName != "" {
		db = db.Where(query.GoodsName.Like("%" + p.GoodsName + "%"))
	}
	if p.GoodsState != "" {
		db = db.Where(query.GoodsStatus.Eq(p.GoodsState))
	}
	if p.VisibleType != "" {
		db = db.Where(query.VisibleType.Eq(p.VisibleType))
	}
	if p.AgencyID != nil {
		agencyIdKind := reflect.TypeOf(p.AgencyID).Kind()
		if agencyIdKind == reflect.Slice || agencyIdKind == reflect.Array {
			ids := lo.Map(cast.ToIntSlice(p.AgencyID), func(item int, index int) int64 {
				return cast.ToInt64(item)
			})
			db = db.Where(fmt.Sprintf(`("%s" is null or "%s" in (?))`, query.AgencyID.ColumnName().String(), query.AgencyID.ColumnName().String()), ids)
		} else if agencyIdKind == reflect.String && cast.ToString(p.AgencyID) == "all" {
			db = db.Where(query.AgencyID.IsNull())
		} else {
			db = db.Where(fmt.Sprintf(`("%s" is null or "%s" = ?)`, query.AgencyID.ColumnName().String(), query.AgencyID.ColumnName().String()), cast.ToInt64(p.AgencyID))
		}
	}
	if p.StartDate != nil && p.EndDate != nil {
		cName := query.TimeRange.ColumnName()
		db = db.Where(fmt.Sprintf(`("%s" is null or "%s" && tstzrange(?, ?))`, cName, cName), p.StartDate, p.EndDate)
	}
	if p.StopTime != nil {
		cName := query.StopTime.ColumnName()
		db = db.Where(fmt.Sprintf(`("%s" is null or "%s" > ?)`, cName, cName), p.StopTime)
	}
	if len(p.SchoolIds) > 0 {
		cName := query.SchoolIds.ColumnName().String()
		db = db.Where(fmt.Sprintf(`("%s" && ARRAY['%s']::VARCHAR(50)[] OR "%s" IS NULL)`, cName, strings.Join(p.SchoolIds, "','"), cName))
	}
	if p.GoodsGroup != "" && p.GoodsGroup != "all" {
		cName := query.GoodsGroups.ColumnName().String()
		db = db.Where(fmt.Sprintf(`("%s" && ARRAY['%s']::VARCHAR(50)[] OR "%s" IS NULL)`, cName, p.GoodsGroup, cName))
	}
	if p.CreatorName != "" {
		db = db.Where(query.CreatorName.Like("%" + p.CreatorName + "%"))
	}
	if p.Status == "1" {
		// 未开始
		db = db.Where(fmt.Sprintf(`"%s" >> '(, ?]'::tstzrange`, query.TimeRange.ColumnName().String()), time.Now())
		db = db.Where(query.StopTime.IsNull())
	} else if p.Status == "2" {
		// 进行中
		cName := query.TimeRange.ColumnName().String()
		db = db.Where(fmt.Sprintf(`("%s" is null or "%s" @> ?::timestamptz)`, cName, cName), time.Now())
		db = db.Where(query.StopTime.IsNull())
		if !p.IsForManagement && p.AgencyID != nil {
			// 非管理端，过滤不可为代理显示的商品
			db = db.Where(query.VisibleType.Eq(common.VisibleTypeVisible))
			subPrams := entity.PaymentGoodsListParams{}
			err := copier.Copy(&subPrams, p)
			if err != nil {
				return 0, nil, err
			}
			subPrams.VisibleType = common.VisibleTypeInvisible
			subPrams.Limit, subPrams.Offset = 0, 0
			subPrams.IsForManagement = true
			_, invisibleGoods, err := a.GetPaymentGoodsList(ctx, subPrams)
			if err != nil {
				return 0, nil, err
			}
			invisibleGoodIDs := lo.Map(invisibleGoods, func(g *channel_model.AgencyGood, _ int) string {
				return g.GoodsID
			})
			invisibleGoodIDs = lo.Uniq(invisibleGoodIDs)
			if len(invisibleGoodIDs) > 0 {
				db = db.Where(query.GoodsID.NotIn(invisibleGoodIDs...))
				db = db.Where(`("combination_goods_id" not in (?)	or "combination_goods_id" is null)`, invisibleGoodIDs)
			}
		}
	} else if p.Status == "3" {
		// 已结束
		db = db.Where(fmt.Sprintf(`"%s" << '(?, )'::tstzrange`, query.TimeRange.ColumnName().String()), time.Now())
		db = db.Where(query.StopTime.IsNull())
	} else if p.Status == "4" {
		// 已终止
		db = db.Where(fmt.Sprintf(`"%s" < ?`, query.StopTime.ColumnName().String()), time.Now())
	}
	if p.StartTime != nil && p.EndTime != nil {
		db = db.Where(fmt.Sprintf(`"%s" >= ? AND "%s" <= ?`, query.CreatedDate.ColumnName().String(), query.CreatedDate.ColumnName().String()), p.StartTime, p.EndTime)
	} else if p.StartTime != nil {
		db = db.Where(fmt.Sprintf(`"%s" >= ?`, query.CreatedDate.ColumnName().String()), p.StartTime)
	} else if p.EndTime != nil {
		db = db.Where(fmt.Sprintf(`"%s" <= ?`, query.CreatedDate.ColumnName().String()), p.EndTime)
	}
	if p.SkuGroupGoodsID != "" {
		db = db.Where(query.SkuGroupGoodsID.Eq(p.SkuGroupGoodsID))
	} else if p.IsCreatedByAgency {
		db = db.Where(query.SkuGroupGoodsID.IsNotNull())
	}
	if p.CreatedAgencyID != "" {
		db = db.Where(query.CreatedAgencyID.Eq(cast.ToInt64(p.CreatedAgencyID)))
	}
	if p.AgencyName != "" {
		agencyQuery := channel_dao.Use(a.data.DB()).Agency
		db = db.Preload("Agency", agencyQuery.Name.Like("%"+p.AgencyName+"%"))
	} else {
		db = db.Preload("Agency")
	}
	db = db.Order(query.CreatedDate.Desc())
	total := int64(0)
	err := db.Count(&total).Error
	if err != nil {
		return 0, nil, err
	}
	if p.Offset > 0 {
		db = db.Offset(p.Offset)
	}
	if p.Limit > 0 {
		db = db.Limit(p.Limit)
	}
	var list []*channel_model.AgencyGood
	err = db.Find(&list).Error
	if err != nil {
		return 0, nil, err
	}
	return cast.ToInt(total), list, nil
}

func (a *agencyGoodRepo) FindMultiByAgency(ctx context.Context, param *entity.AgencyGoodsParams) (int64, []*channel_model.AgencyGood, error) {
	query := channel_dao.Use(a.data.DB()).AgencyGood
	goodsGroup := param.Groups
	if goodsGroup == nil || len(goodsGroup.Groups) == 0 {
		// 默认为代付
		goodsGroup = &entity.AgencyGoodsGroupQuery{
			Groups: []string{common.GoodsGroupAgencyPay},
		}
	}
	db := a.data.DB().WithContext(ctx).Model(&channel_model.AgencyGood{})
	if len(param.GoodKindIdLevel2) > 0 {
		a.log.Info("FindMultiByAgency GoodKindIdLevel2", param.GoodKindIdLevel2)
		db = db.Where(query.GoodKindIDLevel2.In(param.GoodKindIdLevel2...))
	}
	if len(param.GoodsIds) > 0 {
		db = db.Where(query.GoodsID.In(param.GoodsIds...))
	}
	if len(param.GoodsType) > 0 {
		db = db.Where(query.GoodsType.In(param.GoodsType...))
	}
	if param.GoodsName != "" {
		db = db.Where(query.GoodsName.Like("%" + param.GoodsName + "%"))
	}
	if param.GoodsState != "" {
		db = db.Where(query.GoodsStatus.Eq(param.GoodsState))
	}
	if param.IsAllAgency {
		db = db.Where(query.AgencyID.IsNull())
	} else if len(param.AgencyIds) > 0 {
		agencyIdField := query.AgencyID.ColumnName().String()
		db = db.Where(fmt.Sprintf("(%s in (?) or %s is null)", agencyIdField, agencyIdField), param.AgencyIds)
	}
	if param.StartDate != nil && param.EndDate != nil {
		trField := query.TimeRange.ColumnName().String()
		startAt, endAt := param.StartDate.Format("2006-01-02 15:04:05"), param.EndDate.Format("2006-01-02 15:04:05")
		db = db.Where(fmt.Sprintf(`(%s && '["%s","%s"]' or %s is null)`, trField, startAt, endAt, trField))
	}
	if len(param.SchoolIds) > 0 {
		fieldName := query.SchoolIds.ColumnName().String()
		db = db.Where(fmt.Sprintf("(%s && '{%s}' or %s is null)", fieldName, strings.Join(param.SchoolIds, ","), fieldName))
	}
	if goodsGroup != nil && len(goodsGroup.Groups) > 0 {
		fieldName := query.GoodsGroups.ColumnName().String()
		if strings.ToLower(goodsGroup.Logic) == "and" {
			sql := "("
			for i := 0; i < len(goodsGroup.Groups); i++ {
				g := goodsGroup.Groups[i]
				if i > 0 {
					sql += " and "
				}
				sql += fmt.Sprintf(" %s && '{%s}' ", fieldName, g)
			}
			sql += ")"
			db = db.Where(sql)
		} else {
			db = db.Where(fmt.Sprintf(`%s && '{%s}'`, fieldName, strings.Join(goodsGroup.Groups, ",")))
		}
	}
	if param.CreatorName != "" {
		db = db.Where(query.CreatorName.Like("%" + param.CreatorName + "%"))
	}
	if param.VisibleType != "" {
		db = db.Where(query.VisibleType.Eq(param.VisibleType))
	}
	nowStr := time.Now().Format("2006-01-02 15:04:05")
	timeRangeFieldName := query.TimeRange.ColumnName().String()
	if param.Status == "1" { // 未开始
		db = db.Where(fmt.Sprintf(`%s >> '(,%s]'::tstzrange`, timeRangeFieldName, nowStr))
		db = db.Where(query.StopTime.IsNull())
	} else if param.Status == "2" { // 进行中
		db = db.Where(fmt.Sprintf(`(%s is null or %s @> '%s'::timestamptz)`, timeRangeFieldName, timeRangeFieldName, nowStr))
		db = db.Where(query.StopTime.IsNull())
		if !param.IsForManagement && len(param.AgencyIds) > 0 {
			// 非管理端，过滤不可为代理显示的商品
			db = db.Where(query.VisibleType.Eq(common.VisibleTypeVisible))
			subPrams := entity.AgencyGoodsParams{}
			err := copier.Copy(&subPrams, param)
			if err != nil {
				return 0, nil, err
			}
			subPrams.VisibleType = common.VisibleTypeInvisible
			subPrams.Limit, subPrams.Offset = 0, 0
			subPrams.IsForManagement = true
			_, invisibleGoods, err := a.FindMultiByAgency(ctx, &subPrams)
			if err != nil {
				return 0, nil, err
			}
			invisibleGoodIDs := lo.Map(invisibleGoods, func(g *channel_model.AgencyGood, _ int) string {
				return g.GoodsID
			})
			invisibleGoodIDs = lo.Uniq(invisibleGoodIDs)
			if len(invisibleGoodIDs) > 0 {
				db = db.Where(query.GoodsID.NotIn(invisibleGoodIDs...))
				db = db.Where(`("combination_goods_id" not in (?)	or "combination_goods_id" is null)`, invisibleGoodIDs)
			}
		}
	} else if param.Status == "3" { // 已结束
		db = db.Where(fmt.Sprintf(`%s << '(%s,]'::tstzrange`, timeRangeFieldName, nowStr))
		db = db.Where(query.StopTime.IsNull())
	} else if param.Status == "4" { // 已终止
		db = db.Where(fmt.Sprintf(`%s < '%s'`, query.StopTime.ColumnName().String(), nowStr))
	}
	if param.StartDate != nil {
		db = db.Where(query.CreatedDate.Gte(*param.StartDate))
	}
	if param.EndDate != nil {
		db = db.Where(query.CreatedDate.Lte(*param.EndDate))
	}
	if param.SkuGroupGoodsId != "" {
		db = db.Where(query.SkuGroupGoodsID.Eq(param.SkuGroupGoodsId))
	} else if param.IsCreatedByAgency {
		db = db.Where(query.SkuGroupGoodsID.IsNotNull())
	}
	if param.CreatedAgencyId != "" {
		db = db.Where(query.CreatedAgencyID.Eq(cast.ToInt64(param.CreatedAgencyId)))
	}
	if param.AgencyName != "" {
		agencyDb := a.data.DB().WithContext(ctx).Model(&channel_model.Agency{})
		agencyQuery := channel_dao.Use(agencyDb).Agency
		agencyDb = agencyDb.Where(agencyQuery.Name.Like("%" + param.AgencyName + "%"))
		var agencyList []*channel_model.Agency
		if err := agencyDb.Find(&agencyList).Error; err != nil {
			return 0, nil, err
		}
		// 解密敏感字段
		for _, agency := range agencyList {
			if err := agency.DecryptSensitiveFields(); err != nil {
				a.log.Error("DecryptSensitiveFields error:", err)
			}
		}
		if len(agencyList) == 0 {
			return 0, nil, nil
		}
		db = db.Where(query.AgencyID.In(lo.Map(agencyList, func(item *channel_model.Agency, _ int) int64 {
			return item.ID
		})...))
	}
	db = db.Preload(query.Agency.Name())
	total := int64(0)
	err := db.Count(&total).Error
	db = db.Order(fmt.Sprintf(` "%s" desc`, query.CreatedDate.ColumnName().String()))
	if param.Offset > 0 {
		db = db.Offset(cast.ToInt(param.Offset))
	}
	if param.Limit > 0 {
		db = db.Limit(cast.ToInt(param.Limit))
	}
	if err != nil {
		return 0, nil, err
	}
	var list []*channel_model.AgencyGood
	err = db.Find(&list).Error
	if err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (a *agencyGoodRepo) UpdateStatusByGoodIds(ctx context.Context, goodIds []string, status string) error {
	goodIds = lo.Uniq(goodIds)
	goodIds = lo.Filter(goodIds, func(item string, _ int) bool {
		return item != ""
	})
	if len(goodIds) == 0 {
		return nil
	}
	return a.data.DB().WithContext(ctx).Model(&channel_model.AgencyGood{}).Where("goods_id in (?)", goodIds).Update("goods_status", status).Error
}

func (a *agencyGoodRepo) UpdateGroupsByGoodId(ctx context.Context, goodId string, groups []string) error {
	if goodId == "" {
		return nil
	}
	return a.data.DB().WithContext(ctx).Model(&channel_model.AgencyGood{}).Where("goods_id = ?", goodId).Updates(map[string]interface{}{
		"goods_groups": pq.StringArray(groups),
		"updated_date": time.Now(),
	}).Error
}
