package data

import (
	"context"

	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_dao"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_model"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_repo"
	"gitlab.yc345.tv/backend/utils/v2/orm/gen/condition"
	"gitlab.yc345.tv/backend/utils/v2/orm/gen/custom"
)

// AgencyRepoWrapper 包装 AgencyRepo，为绕过 hook 的操作添加加解密逻辑
type AgencyRepoWrapper struct {
	*channel_repo.AgencyRepo
}

// NewAgencyRepoWrapper 创建 AgencyRepo 包装器
func NewAgencyRepoWrapper(repo *channel_repo.AgencyRepo) *AgencyRepoWrapper {
	return &AgencyRepoWrapper{AgencyRepo: repo}
}

// CreateBatch 批量创建数据，添加加密逻辑
func (w *AgencyRepoWrapper) CreateBatch(ctx context.Context, data []*channel_model.Agency, batchSize int) error {
	// 对敏感字段进行加密，因为批量操作可能不会触发 BeforeSave hook
	for _, agency := range data {
		if err := agency.EncryptSensitiveFields(); err != nil {
			return err
		}
	}
	return w.AgencyRepo.CreateBatch(ctx, data, batchSize)
}

// UpdateOne 更新一条数据，添加加密逻辑
func (w *AgencyRepoWrapper) UpdateOne(ctx context.Context, data *channel_model.Agency) error {
	// 创建副本避免修改原始数据
	dataCopy := *data
	// 对敏感字段进行加密，因为 Updates 不会触发 BeforeSave hook
	if err := dataCopy.EncryptSensitiveFields(); err != nil {
		return err
	}
	return w.AgencyRepo.UpdateOne(ctx, &dataCopy)
}

// UpdateOneWithZero 更新一条数据(包含零值)，添加加密逻辑
func (w *AgencyRepoWrapper) UpdateOneWithZero(ctx context.Context, data *channel_model.Agency) error {
	// 创建副本避免修改原始数据
	dataCopy := *data
	// 对敏感字段进行加密，因为 Updates 不会触发 BeforeSave hook
	if err := dataCopy.EncryptSensitiveFields(); err != nil {
		return err
	}
	return w.AgencyRepo.UpdateOneWithZero(ctx, &dataCopy)
}

// UpdateOneByTx 更新一条数据(事务)，添加加密逻辑
func (w *AgencyRepoWrapper) UpdateOneByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency) error {
	// 创建副本避免修改原始数据
	dataCopy := *data
	// 对敏感字段进行加密，因为 Updates 不会触发 BeforeSave hook
	if err := dataCopy.EncryptSensitiveFields(); err != nil {
		return err
	}
	return w.AgencyRepo.UpdateOneByTx(ctx, tx, &dataCopy)
}

// UpdateOneWithZeroByTx 更新一条数据(包含零值)(事务)，添加加密逻辑
func (w *AgencyRepoWrapper) UpdateOneWithZeroByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Agency) error {
	// 创建副本避免修改原始数据
	dataCopy := *data
	// 对敏感字段进行加密，因为 Updates 不会触发 BeforeSave hook
	if err := dataCopy.EncryptSensitiveFields(); err != nil {
		return err
	}
	return w.AgencyRepo.UpdateOneWithZeroByTx(ctx, tx, &dataCopy)
}

// FindMultiByIDS 根据IDS查询多条数据，添加解密逻辑
func (w *AgencyRepoWrapper) FindMultiByIDS(ctx context.Context, IDS []int64) ([]*channel_model.Agency, error) {
	result, err := w.AgencyRepo.FindMultiByIDS(ctx, IDS)
	if err != nil {
		return nil, err
	}
	// 解密敏感字段
	for _, agency := range result {
		if err := agency.DecryptSensitiveFields(); err != nil {
			// 记录错误但不中断流程
			continue
		}
	}
	return result, nil
}

// FindMultiByCustom 自定义查询数据，添加解密逻辑
func (w *AgencyRepoWrapper) FindMultiByCustom(ctx context.Context, customReq *custom.Req) ([]*channel_model.Agency, *custom.Reply, error) {
	result, reply, err := w.AgencyRepo.FindMultiByCustom(ctx, customReq)
	if err != nil {
		return nil, nil, err
	}
	// 解密敏感字段
	for _, agency := range result {
		if err := agency.DecryptSensitiveFields(); err != nil {
			// 记录错误但不中断流程
			continue
		}
	}
	return result, reply, nil
}

// FindMultiByCondition 自定义查询数据(通用)，添加解密逻辑
func (w *AgencyRepoWrapper) FindMultiByCondition(ctx context.Context, conditionReq *condition.Req) ([]*channel_model.Agency, *condition.Reply, error) {
	result, reply, err := w.AgencyRepo.FindMultiByCondition(ctx, conditionReq)
	if err != nil {
		return nil, nil, err
	}
	// 解密敏感字段
	for _, agency := range result {
		if err := agency.DecryptSensitiveFields(); err != nil {
			// 记录错误但不中断流程
			continue
		}
	}
	return result, reply, nil
}
