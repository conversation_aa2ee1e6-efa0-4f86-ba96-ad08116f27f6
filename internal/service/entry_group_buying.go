package service

import (
	"context"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"
	pb "gitlab.yc345.tv/backend/channel/api/entry"
	"gitlab.yc345.tv/backend/channel/internal/biz"
	"gitlab.yc345.tv/backend/channel/internal/common"
	"gitlab.yc345.tv/backend/channel/internal/pkg/middleware/token"
	"google.golang.org/protobuf/types/known/emptypb"
)

func NewEntryGroupBuyingService(
	logger log.Logger,
	baseUC *biz.BaseUseCase,
	uc *biz.EntryGroupBuyingUseCase,
	authDictUC *biz.AuthDictionaryUseCase,
) *EntryGroupBuyingService {
	return &EntryGroupBuyingService{
		log:        log.NewHelper(logger),
		uc:         uc,
		baseUC:     baseUC,
		authDictUC: authDictUC,
	}
}

type EntryGroupBuyingService struct {
	pb.UnimplementedGroupBuyingServer
	uc         *biz.EntryGroupBuyingUseCase
	log        *log.Helper
	baseUC     *biz.BaseUseCase
	authDictUC *biz.AuthDictionaryUseCase
}

func (s *EntryGroupBuyingService) GetGroupBuyList(ctx context.Context, req *pb.GetGroupBuyListReq) (*pb.GetGroupBuyListRes, error) {
	user := token.GetUserFromToken(ctx)
	err := checkJwtRole([]string{common.RoleSuperAdmin, common.RoleAgency, common.RoleAgencyManager, common.RoleSuperVise}, user)
	if err != nil {
		return nil, err
	}
	err = s.authDictUC.CheckAuth(ctx, "entryApply_groupBuy", user)
	if err != nil {
		return nil, err
	}
	res, err := s.uc.GetGroupBuyList(ctx, req, user)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (s *EntryGroupBuyingService) CancelGroupBuy(ctx context.Context, req *pb.EntryIdReq) (*pb.CancelGroupBuyRes, error) {
	user := token.GetUserFromToken(ctx)
	err := checkJwtRole([]string{common.RoleSuperAdmin, common.RoleAgency, common.RoleAgencyManager, common.RoleSuperVise}, user)
	if err != nil {
		return nil, err
	}
	return s.uc.CancelGroupBuy(ctx, req.EntryId, user)
}

func (s *EntryGroupBuyingService) GetGroupBuy(ctx context.Context, req *pb.EntryIdReq) (*pb.GroupBuyingDetail, error) {
	user := token.GetUserFromToken(ctx)
	err := checkJwtRole([]string{common.RoleSuperAdmin, common.RoleAgency, common.RoleAgencyManager, common.RoleSuperVise}, user)
	if err != nil {
		return nil, err
	}
	groupBuy, err := s.uc.GetGroupBuy(ctx, req.EntryId)
	if err != nil {
		return nil, err
	}
	return groupBuy, nil
}

func (s *EntryGroupBuyingService) DecideGroupBuy(ctx context.Context, req *pb.DecideGroupBuyReq) (*emptypb.Empty, error) {
	user := token.GetUserFromToken(ctx)
	err := checkJwtRole([]string{common.RoleSuperAdmin, common.RoleAgencyManager, common.RoleSuperVise}, user)
	if err != nil {
		return nil, err
	}
	err = s.uc.DecideGroupBuy(ctx, req.EntryId, req.State, req.RejectRemark, user)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *EntryGroupBuyingService) CreateGroupBuy(ctx context.Context, req *pb.CreateGroupBuyReq) (*emptypb.Empty, error) {
	user := token.GetUserFromToken(ctx)
	err := checkJwtRole([]string{common.RoleAgencyEmployee, common.RoleAgency}, user)
	if err != nil {
		return nil, err
	}
	err = s.uc.CreateGroupBuy(ctx, user, req)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *EntryGroupBuyingService) GetGroupGoods(ctx context.Context, req *pb.GetGroupGoodsReq) (*pb.GetGroupGoodsRes, error) {
	user := token.GetUserFromToken(ctx)
	if user.Role != common.RoleAgency && user.Role != common.RoleAgencyEmployee {
		return nil, errors.Forbidden("无权限操作", "无权限操作")
	}
	rows, err := s.uc.GetGroupGoods(ctx, user, req.SchoolId)
	if err != nil {
		return nil, err
	}
	return &pb.GetGroupGoodsRes{
		Rows: lo.UniqBy(rows, func(item *pb.GroupGoodItem) string {
			if item == nil {
				return ""
			}
			return item.GoodsId
		}),
	}, nil
}
