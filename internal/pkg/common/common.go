package common

import (
	"errors"
	"github.com/jackc/pgtype"
	yccrypt "gitlab.yc345.tv/backend/yccrypt/go"
)

func CheckNilInstance[T any, R any](t *T, fn func(T) *R) *R {
	if t == nil {
		return nil
	}
	return fn(*t)
}

func PgJSON2Str(input *pgtype.JSON) *string {
	if input == nil {
		return nil
	}
	buff, _ := input.MarshalJSON()
	s := string(buff)
	return &s
}

func EncryptSensitive(input string) (string, error) {
	if input == "" {
		return "", nil
	}
	if len(input) >= 20 {
		// 可能已经被加密
		// return input, nil
		return "", errors.New("sensitive data has been encrypted")
	}
	ycCrypter := yccrypt.NewYCCrypterFromEnv()
	phone, err := ycCrypter.SearchableCryptHex(input)
	if err != nil {
		return "", err
	}
	return phone, nil
}

func DecryptSensitive(input string) (string, error) {
	if input == "" {
		return "", nil
	}
	if len(input) == 11 {
		// 可能已经被解密
		// return input, nil
		return "", errors.New("sensitive data is already decrypted")
	}
	ycCrypter := yccrypt.NewYCCrypterFromEnv()
	phone, err := ycCrypter.SearchableDecryptHex(input)
	if err != nil {
		return "", err
	}
	return phone, nil
}
