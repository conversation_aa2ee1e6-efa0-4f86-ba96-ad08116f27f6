package common

import (
	"github.com/jackc/pgtype"
)

func CheckNilInstance[T any, R any](t *T, fn func(T) *R) *R {
	if t == nil {
		return nil
	}
	return fn(*t)
}

func PgJSON2Str(input *pgtype.JSON) *string {
	if input == nil {
		return nil
	}
	buff, _ := input.MarshalJSON()
	s := string(buff)
	return &s
}

curl --location --request GET 'http://channel.teacherschool/internal/channel/entry/group-buying/is-show?type=isShowGroupBuying' \
--header 'uid: 65fa924e3e3a8f0001a41353'