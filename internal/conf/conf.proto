syntax = "proto3";
package config;

option go_package = "gitlab.yc345.tv/backend/channel/internal/conf;conf";

//import "api/channel/channel.proto";
import "google/protobuf/duration.proto";

// 环境变量GO_ENV选项
enum GO_ENV  {
  production = 0;
  stage = 1;
  test = 2;
  local = 3;
}

message Bootstrap {
  message YC {
    map<string, Redis> redis = 1;
    map<string, PG> pg = 2;
    Nacos nacos = 3;
    
  }
  string name = 1;
  Server server = 2;
  GO_ENV env = 3;
  YC yc = 4;
  Logger logger = 5;
  RateLimit ratelimit = 7;
  string yapiProjectId = 8;
  YcOss ycoss = 9;
  Feishu feishu = 10;
  Jwt jwt = 11;
  CronTasks cronTasks = 12;
  int32 hiddenStudyTourAgency = 13;
  repeated string goodKindIdLevel2For998 = 14;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

// Redis 缓存数据.
message Redis {
  string addr = 1;
  string username = 2;
  string password = 3;
  int32 db = 4;
  // 读超时时间，默认3秒，-1表示取消读超时.
  google.protobuf.Duration readTimeout = 5;
  // 写超时时间，默认等于读超时.
  google.protobuf.Duration writeTimeout = 6;
  string keyPrefix = 7;
  // 触发 LatencyHookFn 函数的时间.
  google.protobuf.Duration maxLatency = 8;
  // 连接池的类型。FIFO 池为 true，LIFO 池为 false。请注意，与 lifo 相比，fifo 的开销更高.
  bool poolFIFO = 9;
  int32 poolSize = 10;
  // 如果所有连接都忙，则客户端在返回错误之前等待连接的时间.
  google.protobuf.Duration poolTimeout = 11;
  // 建立新连接缓慢时有用的最小空闲连接数.
  int32 minIdleConns = 12;
  // 客户端关闭空闲连接的时间。应该小于服务器的超时时间.
  google.protobuf.Duration idleTimeout = 13;
  // 客户端退出（关闭）连接的连接年龄。默认是不关闭老化的连接.
  google.protobuf.Duration maxConnAge = 14;
  string name = 15;
}

// PG pg配置信息
message PG {
  string host = 1;
  int32 port = 2;
  string user = 3;
  string password = 4;
  string db = 5;
  string sslMode = 6;
  // 最大空闲连接数.
  int32 maxIdleConns = 7;
  // 最大活动连接数.
  int32 maxOpenConns = 8;
  // 连接的最大存活时间 300s.
  google.protobuf.Duration connMaxLifetime = 9;
  // 是否开启debug模式.
  bool debug = 10;
  // 关闭复数表 true.
  bool singularTable = 11;
  // 关闭指标采集.
  bool disableMetric = 12;
  // 超时时间 500ms.
  google.protobuf.Duration slowThreshold = 13;
  // 更新字段.
  repeated string updateColumn = 14;
  // 名称
  string name = 15;
}

// logger 日志配置信息
message Logger {
  repeated string headerWhites = 1;
  int32 enableResWithCode = 2;
  int32 enableReqWithCode = 3;
}

message Nacos {
  string host = 1;
  uint64 port = 2;
  string username = 3;
  string password = 4;
}
message RateLimit {
  int64 window = 1;
  int64 bucket = 2;
  int64 cpuThreshold = 3;
}

message YcOss {// 洋葱OSS存储
  string domain = 1;
  string accessToken = 2;
  string bucket = 3;
}

message Feishu {
  string approvalCode = 1;
  string invoiceApprovalCode = 2;
  string entryInvoiceApprovalCode = 3;
  string userId = 4;
  string openId = 5;
  string departmentId = 6;
  string clientId = 7;
  string clientSecret = 8;
  string agencyUserAlert = 9;
  string offlineOrderApprovalCode = 10;
}

message Jwt {
  string secret = 1;
  string shadowSecret = 2;
  string expiresIn = 3;
}

// CronTasks 定时任务
message CronTasks {
  enum NOTIFY {
    // NONE 不通知.
    NONE = 0;
    // 打印日志.
    CONSOLE = 1;
    // 飞书通知.
    FEISHU = 2;
  }

  // 飞书通知结构.
  message Feishu {
    // 通知的飞书机器人url.
    string url = 1;
    // 标题.
    string title = 2;
  }

  message CronTask {
    // 名称.
    string name = 1;
    // cron表达式.
    string cron = 2;
    // 运行模式 default:到时间就开一个新的协程处理任务; skip:该执行的时候,上一个还没结束,就跳过本次运行; delay: 延迟继续运行.
    string mode = 3;
    // 通知方式; feishu: 飞书通知; console 打印日志.
    NOTIFY notify = 4;
    // 飞书通知结构(每个任务可以设置独立的通知, 优先使用).
    Feishu feishu = 5;
  }

  // 通知方式; feishu: 飞书通知; console 打印日志.
  NOTIFY notify = 1;
  // 飞书通知结构(所有任务共用的飞书结构).
  Feishu feishu = 2;
  // task1的结构.
  CronTask task1 = 3;
  // task2的结构.
  CronTask task2 = 4;
  CronTask refreshGood = 5;
}
