package biz

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/jinzhu/now"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	pb "gitlab.yc345.tv/backend/channel/api/entry"
	"gitlab.yc345.tv/backend/channel/clients"
	domainEntity "gitlab.yc345.tv/backend/channel/internal/biz/domain/entity"
	domain "gitlab.yc345.tv/backend/channel/internal/biz/domain/role"
	"gitlab.yc345.tv/backend/channel/internal/biz/entity"
	"gitlab.yc345.tv/backend/channel/internal/biz/iface"
	"gitlab.yc345.tv/backend/channel/internal/common"
	"gitlab.yc345.tv/backend/channel/internal/conf"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_model"
	"gitlab.yc345.tv/backend/channel/internal/pkg/middleware/token"
	goodCommon "gitlab.yc345.tv/backend/good/api/common"
	"gitlab.yc345.tv/backend/good/api/good"
	"golang.org/x/exp/maps"
	"golang.org/x/sync/errgroup"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"
	"strings"
)

type EntryGroupBuyingUseCase struct {
	log                      *log.Helper
	config                   *conf.Bootstrap
	groupBuyingRepo          iface.IEntryGroupBuyingRepo
	groupBuyingGoodRepo      iface.IEntryGroupBuyingGoodRepo
	roleMgr                  domain.IRoleFactory
	agencyRepo               iface.IAgencyRepo
	thirdPartRepo            iface.IThirdPartRepo
	userRepo                 iface.IUserRepo
	entryRepo                iface.IEntryRepo
	orderClient              clients.OrderHTTPClient
	goTeacherSchoolBusClient clients.GoTeacherSchoolBusHTTPClient
	baseUc                   *BaseUseCase
	agencyGoodRepo           iface.IAgencyGoodRepo
	orderExtendedClient      clients.OrderExtendedHttpClient
	courseBffExtendedClient  clients.CourseBffExtendedHttpClient
	specialCoursePkgRepo     iface.ISpecialCoursePkgRepo
	schoolClient             clients.SchoolThirdHTTPClient
	goodClient               clients.GoodExtendedHttpClient
}

func NewEntryGroupBuyingUseCase(
	logger log.Logger,
	config *conf.Bootstrap,
	groupBuyingRepo iface.IEntryGroupBuyingRepo,
	groupBuyingGoodRepo iface.IEntryGroupBuyingGoodRepo,
	roleMgr domain.IRoleFactory,
	agencyRepo iface.IAgencyRepo,
	thirdPartRepo iface.IThirdPartRepo,
	userRepo iface.IUserRepo,
	entryRepo iface.IEntryRepo,
	orderClient clients.OrderHTTPClient,
	goTeacherSchoolBusClient clients.GoTeacherSchoolBusHTTPClient,
	baseUc *BaseUseCase,
	agencyGoodRepo iface.IAgencyGoodRepo,
	orderExtendedClient clients.OrderExtendedHttpClient,
	courseBffExtendedClient clients.CourseBffExtendedHttpClient,
	specialCoursePkgRepo iface.ISpecialCoursePkgRepo,
	schoolClient clients.SchoolThirdHTTPClient,
	goodClient clients.GoodExtendedHttpClient,
) *EntryGroupBuyingUseCase {
	return &EntryGroupBuyingUseCase{
		log:                      log.NewHelper(logger),
		config:                   config,
		groupBuyingRepo:          groupBuyingRepo,
		groupBuyingGoodRepo:      groupBuyingGoodRepo,
		roleMgr:                  roleMgr,
		agencyRepo:               agencyRepo,
		thirdPartRepo:            thirdPartRepo,
		userRepo:                 userRepo,
		entryRepo:                entryRepo,
		orderClient:              orderClient,
		goTeacherSchoolBusClient: goTeacherSchoolBusClient,
		baseUc:                   baseUc,
		agencyGoodRepo:           agencyGoodRepo,
		orderExtendedClient:      orderExtendedClient,
		courseBffExtendedClient:  courseBffExtendedClient,
		specialCoursePkgRepo:     specialCoursePkgRepo,
		schoolClient:             schoolClient,
		goodClient:               goodClient,
	}
}

func (uc *EntryGroupBuyingUseCase) GetGroupBuyList(ctx context.Context, req *pb.GetGroupBuyListReq, user *token.User) (*pb.GetGroupBuyListRes, error) {
	searchRegion := lo.If(req.CountyCode != "", req.CountyCode).ElseIf(req.CityCode != "", req.CityCode).Else(req.ProvinceCode)
	role := user.Role
	if role == common.RoleAgency && user.IsEmployee {
		role = common.RoleAgencyEmployee
	}
	var shortCodes []int32
	if lo.Contains([]string{common.RoleAgencyManager, common.RoleSuperVise, common.RoleAgency, common.RoleAgencyEmployee}, role) {
		roleDomain := uc.roleMgr.FindRole(role)
		schools, err := roleDomain.GetAllAvailableSchools(ctx, roleDomain, user.ID)
		if err != nil {
			return nil, err
		}
		shortCodes = lo.Map(schools, func(s domainEntity.MaintainSchool, _ int) int32 {
			return s.ShortCode
		})
	}
	var ss []string
	if req.StageSubject != "" {
		ss = []string{req.StageSubject}
	}
	params := entity.GroupBuyingListParam{
		SearchRegion:    searchRegion,
		AgencyId:        cast.ToInt64(user.AgencyID),
		Role:            role,
		RoleId:          user.ID,
		ShortCodes:      shortCodes,
		BeforeBeginDate: req.BeforeBeginDate,
		Phone:           req.Phone,
		StageSubject:    ss,
		EntryId:         req.EntryId,
		RoomRef:         req.RoomRef,
		SchoolId:        req.SchoolId,
		ValidateDate:    req.ValidateDate,
		ExpireDate:      req.ExpireDate,
		State:           req.State,
		Sort:            req.Sort,
		Limit:           cast.ToInt(req.Limit),
		Offset:          cast.ToInt(req.Offset),
	}
	total, list, err := uc.groupBuyingRepo.GetGroupBuyingListByParams(ctx, &params)
	if err != nil {
		return nil, err
	}
	var agencyIds []string
	for i := 0; i < len(list); i++ {
		item := list[i]
		list[i].StageSubject = common.GetStageListName(item.StageSubject)
		agencyIds = append(agencyIds, cast.ToString(item.AgencyID))
	}
	agencyIds = lo.Filter(lo.Uniq(agencyIds), func(item string, _ int) bool {
		return item != ""
	})
	agencies, err := uc.agencyRepo.GetAgencyByIDs(ctx, agencyIds)
	if err != nil {
		return nil, err
	}
	agencyMap := lo.SliceToMap(agencies, func(a channel_model.Agency) (int64, *pb.Agency) {
		agency := pb.Agency{
			ChargePhone:                  a.ChargePhone,
			FinancePhone:                 a.FinancePhone,
			BusinessPhone:                a.BusinessPhone,
			Id:                           cast.ToString(a.ID),
			Name:                         a.Name,
			OldName:                      a.OldName,
			ChargeName:                   a.ChargeName,
			Deleted:                      a.Deleted,
			CreateUserId:                 a.CreateUserID,
			UpdateUserId:                 lo.FromPtr(a.UpdateUserID),
			Status:                       a.Status,
			AgencyType:                   a.AgencyType,
			EnterpriseName:               a.EnterpriseName,
			EnterpriseType:               a.EnterpriseType,
			EnterpriseLegalPerson:        a.EnterpriseLegalPerson,
			EnterpriseAddress:            a.EnterpriseAddress,
			ReceiverAddress:              a.ReceiverAddress,
			InvoiceTitle:                 a.InvoiceTitle,
			TaxpayerIdentificationNumber: a.TaxpayerIdentificationNumber,
			TaxpayerQualification:        a.TaxpayerQualification,
			BusinessLicense:              a.BusinessLicense,
			CooperationFeeFile:           a.CooperationFeeFile,
			PrepayImg:                    a.PrepayImg,
			IDPhoto:                      a.IDPhoto,
			SchoolReserveCount:           a.SchoolReserveCount,
			OnionEmployeeCount:           a.OnionEmployeeCount,
			GuestDesc:                    a.GuestDesc,
			OtherProduct:                 a.OtherProduct,
			SignedMan:                    a.SignedMan,
			SignedManPhone:               a.SignedManPhone,
			TeamDesc:                     a.TeamDesc,
			Remark:                       a.Remark,
			CustomerPhone:                a.CustomerPhone,
			AgencyLevel:                  a.AgencyLevel,
			BaseInfo:                     a.BaseInfo,
			CreatedDate:                  timestamppb.New(a.CreatedDate),
		}
		return a.ID, &agency
	})
	res := pb.GetGroupBuyListRes{
		Total:        cast.ToUint32(total),
		GroupBuyList: nil,
	}
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	res.GroupBuyList = lo.Map(list, func(g *channel_model.EntryGroupBuying, _ int) *pb.GroupBuyItem {
		agency := agencyMap[g.AgencyID]
		d := g.Detail
		entry := pb.EntryInfo{
			Id:           cast.ToInt32(d.ID),
			Type:         d.Type,
			State:        d.State,
			Remark:       &d.Remark,
			CreatedDate:  timestamppb.New(d.CreatedDate),
			CreateUserID: lo.FromPtrOr(d.CreateUserID, ""),
			UpdateUserID: d.UpdateUserID,
			RejectRemark: &d.RejectRemark,
		}
		var cancelAt *timestamppb.Timestamp
		if g.CancelDate.Valid {
			cancelAt = timestamppb.New(g.CancelDate.Time)
		}
		var noticeInfo pb.NoticeInfo
		bys, _ := g.NoticeInfo.MarshalJSON()
		_ = unmarshaler.Unmarshal(bys, &noticeInfo)
		item := pb.GroupBuyItem{
			EntryId:      cast.ToString(g.EntryID),
			ValidateDate: timestamppb.New(g.ValidateDate),
			ExpireDate:   timestamppb.New(g.ExpireDate),
			StageSubject: g.StageSubject,
			RoomId:       g.RoomID,
			RoomRef:      g.RoomRef,
			RoomName:     g.RoomName,
			Uids:         g.Uids,
			SchoolId:     g.SchoolID,
			SchoolName:   g.SchoolName,
			Region:       g.Region,
			TeacherId:    g.TeacherID,
			TeacherName:  g.TeacherName,
			StudentCount: g.StudentCount,
			AgencyId:     cast.ToString(g.AgencyID),
			ActivityId:   lo.FromPtr(g.ActivityID),
			CreateUserId: lo.FromPtr(g.CreateUserID),
			// CancelResult:          g.CancelResult,
			CancelUserId:          lo.FromPtr(g.CancelUserID),
			GearboxCancelUserId:   g.GearboxCancelUserID,
			GearboxCancelUserName: g.GearboxCancelUserName,
			CancelDate:            cancelAt,
			CancelRemark:          g.CancelRemark,
			NoticeInfo:            &noticeInfo,
			CreatedDate:           timestamppb.New(g.CreatedDate),
			Agency:                agency,
			Detail:                &entry,
		}
		return &item
	})
	return &res, nil
}

func (uc *EntryGroupBuyingUseCase) CancelGroupBuy(ctx context.Context, entryId int32, user *token.User) (*pb.CancelGroupBuyRes, error) {
	// entry, err := uc.entryRepo.FindOneByID(ctx, cast.ToInt64(entryId))
	// if err != nil {
	// 	return nil, err
	// }
	// if entry.State != common.EntryStateAuditing {
	// 	err = uc.orderExtendedClient.RemoveGroupBuying(ctx, &clients.GetOrderByIdReq{OrderId: cast.ToString(entryId)})
	// 	if err != nil {
	// 		uc.log.Errorf("remove group buy failed, %v", err)
	// 		return nil, errors.InternalServer("代理商优惠终止失败", "代理商优惠终止失败")
	// 	}
	// }
	updateInfo := channel_model.EntryGroupBuying{
		EntryID: cast.ToInt64(entryId),
		CancelDate: sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		},
		CancelResult: datatypes.JSON{},
	}
	err := uc.groupBuyingRepo.UpdateGroupBuy(ctx, &updateInfo, user, common.EntryStateAuditCancel)
	if err != nil {
		return nil, err
	}
	return &pb.CancelGroupBuyRes{
		Ret1: &emptypb.Empty{},
		Ret2: []int32{1},
	}, nil
}

func (uc *EntryGroupBuyingUseCase) GetGroupBuy(ctx context.Context, entryId int32) (*pb.GroupBuyingDetail, error) {
	groupBuy, err := uc.groupBuyingRepo.FindOneWithGoodsByEntryId(ctx, int64(entryId))
	if err != nil {
		return nil, err
	}
	roomRef := groupBuy.RoomRef
	room, err := uc.thirdPartRepo.GetRoomByRef(ctx, roomRef)
	if err != nil {
		return nil, err
	}
	if room == nil || room.Id == "" {
		return nil, errors.New(http.StatusConflict, "该班级已删除", "该班级已删除")
	}
	roomGroupBuyCount, err := uc.groupBuyingRepo.GetGroupBuyCountOneYear(ctx, roomRef, nil, 0)
	if err != nil {
		return nil, err
	}
	agencyGroupBuyCount, err := uc.groupBuyingRepo.GetGroupBuyCountOneYear(ctx, "", nil, groupBuy.AgencyID)
	if err != nil {
		return nil, err
	}
	baseUser, err := uc.userRepo.GetUserByID(ctx, lo.FromPtr(groupBuy.CreateUserID))
	if err != nil {
		return nil, err
	}
	user := entity.UserWithRegions{
		User: *baseUser,
	}
	roleMgr := uc.roleMgr.FindRole(user.Role)
	directRegions, err := roleMgr.GetDirectAdminRegions(ctx, user.ID)
	if err != nil {
		return nil, err
	}
	directSchools, err := roleMgr.GetDirectAdminSchools(ctx, user.ID)
	if err != nil {
		return nil, err
	}
	regions := lo.Map(directSchools.Rows, func(s domainEntity.MaintainSchool, index int) string {
		return s.RegionCode
	})
	regions = append(regions, directRegions.Rows...)
	user.Regions = lo.Uniq(regions)
	// 给审核的人一个提醒
	regionChange := lo.If(room.School.RegionCode == groupBuy.Region, "").Else("(区域发生变动)")
	region, err := uc.thirdPartRepo.GetRegionTreeName(ctx, room.School.RegionCode)
	if err != nil {
		return nil, err
	}
	agency, err := uc.agencyRepo.GetAgency(ctx, cast.ToString(groupBuy.AgencyID))
	if err != nil {
		return nil, err
	}
	date := fmt.Sprintf(`%s ~ %s；共%d天`, groupBuy.ValidateDate.Format("2006-01-02"), groupBuy.ExpireDate.Format("2006-01-02"), groupBuy.ExpireDate.Sub(groupBuy.ValidateDate)/(time.Hour*24))
	cancelDate := ""
	if groupBuy.CancelDate.Valid {
		cancelDate = groupBuy.CancelDate.Time.Format(time.RFC3339)
	}
	cancelUserName := "--"
	if groupBuy.GearboxCancelUserName != "" {
		cancelUserName = groupBuy.GearboxCancelUserName
	} else if groupBuy.CancelUserOfGroupBuying != nil && groupBuy.CancelUserOfGroupBuying.Name != "" {
		cancelUserName = groupBuy.CancelUserOfGroupBuying.Name
	}
	combineGoodIds := lo.Map(lo.Filter(groupBuy.Goods, func(g channel_model.EntryGroupBuyingGood, _ int) bool {
		return g.IsCombination
	}), func(g channel_model.EntryGroupBuyingGood, _ int) string {
		return g.GoodsID
	})
	combineGoodMap := make(map[string]*goodCommon.SkuGoodInfo)
	if len(combineGoodIds) > 0 {
		comBineGoods, err := uc.goodClient.GetAllCombinationGoodsByIds(ctx, combineGoodIds, nil, nil)
		if err != nil {
			return nil, err
		}
		combineGoodMap = lo.SliceToMap(comBineGoods, func(g *goodCommon.SkuGoodInfo) (string, *goodCommon.SkuGoodInfo) {
			return g.Id, g
		})
	}
	goods := lo.Map(groupBuy.Goods, func(g channel_model.EntryGroupBuyingGood, _ int) *pb.GroupBuyingGoods {
		r := &pb.GroupBuyingGoods{
			Id:                   g.ID,
			EntryId:              cast.ToString(g.EntryID),
			EntryIdBak:           cast.ToString(g.EntryID),
			GoodsId:              g.GoodsID,
			GoodsName:            g.GoodsName,
			GoodsNamePrefix:      g.GoodsNamePrefix,
			GoodsType:            g.GoodsType,
			GoodsPay:             fmt.Sprintf("%.2f", g.GoodsPay),
			GoodsGroupPay:        fmt.Sprintf("%.2f", g.GoodsGroupPay),
			ChannelGoodsGroupPay: fmt.Sprintf("%.2f", g.ChannelGoodsGroupPay),
			SettlementPrices:     fmt.Sprintf("%.2f", g.SettlementPrices),
			StageSubject:         g.Stagesubject,
			Time:                 g.Time,
			CreateDate:           g.CreatedDate.Format("2006-01-02T15:04:05Z07:00"),
			CreatedDate:          g.CreatedDate.Format("2006-01-02T15:04:05Z07:00"),
		}
		if g.UpdatedDate.Valid {
			r.UpdateDate = g.UpdatedDate.Time.Format("2006-01-02T15:04:05Z07:00")
		}
		cb := combineGoodMap[g.GoodsID]
		if cb != nil {
			r.RequiredItemList = cb.RequiredItemList
			r.MandatoryItemList = cb.MandatoryItemList
			r.AdditionalItemList = cb.AdditionalItemList
		}
		return r
	})
	var notice pb.NoticeInfo
	bys, err := groupBuy.NoticeInfo.MarshalJSON()
	if err := json.Unmarshal(bys, &notice); err != nil {
		return nil, err
	}
	res := pb.GroupBuyingDetail{
		EntryId:             cast.ToString(groupBuy.EntryID),
		RoomId:              room.GroupId,
		RoomRef:             room.Ref,
		RoomName:            room.Name,
		CancelDate:          cancelDate,
		CancelUserName:      cancelUserName,
		StudentCount:        cast.ToInt32(len(room.Members)),
		AgencyId:            cast.ToString(agency.ID),
		SchoolId:            room.School.Id,
		SchoolName:          room.School.Name,
		StageId:             room.School.StageId,
		SchoolRegion:        room.School.RegionCode,
		County:              region.County.Name + regionChange,
		City:                region.City.Name,
		Date:                date,
		StageSubject:        common.GetStageListName(groupBuy.StageSubject),
		Creator:             user.Name,
		AgencyName:          agency.Name,
		RoomGroupBuyCount:   cast.ToInt32(roomGroupBuyCount),
		AgencyGroupBuyCount: cast.ToInt32(agencyGroupBuyCount),
		CreatedDate:         groupBuy.Detail.CreatedDate.Format(time.RFC3339),
		ExpireDate:          groupBuy.ExpireDate.Format(time.RFC3339),
		Remark:              groupBuy.Detail.Remark,
		Goods:               goods,
		NoticeInfo:          &notice,
	}
	return &res, nil
}

func (uc *EntryGroupBuyingUseCase) DecideGroupBuy(ctx context.Context, entryId, state int32, rejectRemark string, user *token.User) error {
	if state == common.EntryStateAuditPass {
		entryGroupBuy, err := uc.groupBuyingRepo.FindOneWithGoodsByEntryId(ctx, cast.ToInt64(entryId))
		if err != nil {
			return err
		}
		room, err := uc.thirdPartRepo.GetRoomByRef(ctx, entryGroupBuy.RoomRef)
		if err != nil {
			uc.log.Errorf("get room by ref failed, %v", err)
			return errors.InternalServer("获取班级失败", "获取班级失败")
		}
		// p := clients.InsertGroupBuyingReq{
		// 	UserIdList:       room.Members,
		// 	EntryId:          cast.ToString(entryId),
		// 	StageSubjectList: entryGroupBuy.StageSubject,
		// 	StartTime:        entryGroupBuy.ValidateDate.Format(time.RFC3339),
		// 	EndTime:          entryGroupBuy.ExpireDate.Format(time.RFC3339),
		// }
		// err = uc.orderExtendedClient.InsertGroupBuying(ctx, &p)
		// if err != nil {
		// 	uc.log.Errorf("insert group buying failed, %v", err)
		// 	return errors.InternalServer("代理商优惠创建失败", "代理商优惠创建失败")
		// }
		err = uc.groupBuyingRepo.UpdateOne(ctx, &channel_model.EntryGroupBuying{EntryID: cast.ToInt64(entryId), Uids: room.Members})
		if err != nil {
			uc.log.Errorf("update group buying failed, %v", err)
			return errors.InternalServer("团购记录更新失败", "团购记录更新失败")
		}
		var validateDate time.Time
		if !entryGroupBuy.Detail.UpdatedDate.Valid || entryGroupBuy.ValidateDate.After(entryGroupBuy.Detail.UpdatedDate.Time) {
			validateDate = entryGroupBuy.ValidateDate
		} else {
			validateDate = entryGroupBuy.Detail.UpdatedDate.Time
		}
		if entryGroupBuy.NoticeInfo != nil {
			var notice pb.NoticeInfo
			bys, err := entryGroupBuy.NoticeInfo.MarshalJSON()
			if err != nil {
				return err
			}
			if err = json.Unmarshal(bys, &notice); err != nil {
				return err
			}
			err = uc.sendGroupBuyDelayMessage(ctx, &notice, cast.ToString(entryId), validateDate, entryGroupBuy.ExpireDate, room.Ref, entryGroupBuy.StageSubject)
			if err != nil {
				uc.log.Errorf("send group buy delay message failed, %v", err)
				return errors.InternalServer("团购记录更新失败", "团购记录更新失败")
			}
		}
	}
	updateInfo := channel_model.Entry{
		ID:    cast.ToInt64(entryId),
		State: state,
	}
	if user.IsGearbox {
		updateInfo.GearboxUpdateUserID = user.ID
		updateInfo.GearboxUpdateUserName = user.Name
	} else {
		updateInfo.UpdateUserID = &user.ID
	}
	if rejectRemark != "" {
		updateInfo.RejectRemark = rejectRemark
	}
	err := uc.entryRepo.UpdateOne(ctx, &updateInfo)
	if err != nil {
		return err
	}
	err = uc.baseUc.CreateEntryLog(ctx, cast.ToInt64(entryId), state, user.Name, common.EntryTypeGroupBuying)
	return err
}

func (uc *EntryGroupBuyingUseCase) sendGroupBuyDelayMessage(ctx context.Context, noticeInfo *pb.NoticeInfo, entryId string, validateDate, expiredDate time.Time, roomRef string, stageSubject []string) error {
	uc.log.Infof("send group buy delay message, entryId: %s, validateDate: %s, expiredDate: %s, roomRef: %s, stageSubject: %v，noticeInfo：%v", entryId, validateDate, expiredDate, roomRef, stageSubject, noticeInfo)
	var messageList []*clients.DelayMessage
	msgObj := entity.MsgObj{
		NoticeInfo:   noticeInfo,
		EntryId:      entryId,
		ValidateDate: validateDate.Format(time.RFC3339),
		ExpiredDate:  expiredDate.Format(time.RFC3339),
		RoomRef:      roomRef,
		StageSubject: stageSubject,
		Type:         "popup",
	}
	popupContent, err := json.Marshal(msgObj)
	if err != nil {
		return err
	}
	msgObj.Type = "open"
	openContent, err := json.Marshal(msgObj)
	if err != nil {
		return err
	}
	date := now.New(validateDate).BeginningOfDay()
	// 时间小于现在明天0时发
	if date.Before(time.Now()) {
		// 立即发送
		messageList = append(messageList, &clients.DelayMessage{
			Trigger: time.Now().Add(uc.lsTime() * time.Millisecond).Local().Format(common.DateTime),
			Content: string(openContent),
		}, &clients.DelayMessage{
			Trigger: time.Now().Add(uc.lsTime() * time.Millisecond).Local().Format(common.DateTime),
			Content: string(popupContent),
		})
		date = date.AddDate(0, 0, 1)
	}
	// 延时发送
	popupTime := date
	for popupTime.Before(expiredDate) {
		messageList = append(messageList, &clients.DelayMessage{
			Trigger: popupTime.Add(uc.lsTime() * time.Millisecond).Local().Format(common.DateTime),
			Content: string(popupContent),
		})
		popupTime = popupTime.AddDate(0, 0, 1)
	}
	// 前端已经无通知频次逻辑，故跳过通知频次逻辑, 直接发送
	_, err = uc.goTeacherSchoolBusClient.SendDelayMessage(ctx, &clients.SendDelayMessageReq{
		Scene:       "teacher_group_buy",
		CallbackUrl: "http://channel-platform-server.teacherschool/api/group-buying/message/callback",
		MsgList:     messageList,
	})
	if err != nil {
		return err
	}
	return nil
}

// 返回数据单位：毫秒
func (uc *EntryGroupBuyingUseCase) lsTime() time.Duration {
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Intn(60)
	timeInterval := (randomNumber + 20) * 1000
	return time.Duration(timeInterval)
}

func (uc *EntryGroupBuyingUseCase) CreateGroupBuy(ctx context.Context, user *token.User, req *pb.CreateGroupBuyReq) error {
	// 检查商品时间范围
	goodsIds := maps.Keys(req.GoodsIds)
	goodsMinTime, goodsMaxTime, err := uc.checkGoodsTimeRange(ctx, goodsIds, user.AgencyID)
	if err != nil {
		return err
	}
	if goodsMinTime != nil && goodsMaxTime != nil {
		if !((goodsMinTime.Before(req.StartTime.AsTime()) || goodsMinTime.Equal(req.StartTime.AsTime())) && (goodsMaxTime.After(req.EndTime.AsTime()) || goodsMaxTime.Equal(req.EndTime.AsTime()))) {
			return errors.BadRequest("开始结束时间超出商品范围", "开始结束时间超出商品范围")
		}
	}
	// 获取班级信息
	room, err := uc.thirdPartRepo.GetRoomByRef(ctx, req.ClassRef)
	if err != nil {
		uc.log.Errorf("get room by ref failed, %v", err)
		return errors.InternalServer("获取班级异常", "获取班级异常")
	}
	isAdminRoom := room != nil && room.Id != "" && (room.AdminRoomOrRoom == "admin" || room.Type == "admin")
	if !isAdminRoom {
		return errors.BadRequest("教学班已不支持团购促销，请添加到行政班", "教学班已不支持团购促销，请添加到行政班")
	}
	// 判断是否是维护班级
	mgr := uc.roleMgr.FindRole(user.Role)
	schools, err := mgr.GetAllAvailableSchools(ctx, mgr, user.ID)
	if err != nil {
		return err
	}
	isMaintainRoom := false
	if room.Id != "" {
		for _, school := range schools {
			if school.ID == room.SchoolId {
				isMaintainRoom = true
				break
			}
		}
	}
	if !isMaintainRoom {
		return errors.Forbidden("请先添加该班级至维护班级", "请先添加该班级至维护班级")
	}
	// 创建 Entry
	entry := channel_model.Entry{
		Type:        common.EntryTypeGroupBuying,
		State:       common.EntryStateAuditing,
		Remark:      req.Remark,
		CreatedDate: time.Now(),
	}
	if user.IsGearbox {
		entry.GearboxCreateUserID = user.ID
		entry.GearboxCreateUserName = user.Name
	} else {
		entry.CreateUserID = &user.ID
	}
	err = uc.entryRepo.CreateOneWithoutFields(ctx, &entry, []string{"update_user_id"})
	if err != nil {
		uc.log.Errorf("创建 Entry 失败: %v", err)
		return errors.InternalServer("创建 Entry 失败", "创建 Entry 失败")
	}
	commonGoodIds := lo.Filter(goodsIds, func(gid string, _ int) bool {
		return !common.IsUUID(gid)
	})
	combinationGoodIds := lo.Filter(goodsIds, func(gid string, _ int) bool {
		return common.IsUUID(gid)
	})
	goodsNamePrefix := req.GoodsNamePrefix
	var commonGoods []*good.GoodInfo
	var combinationGoods []*goodCommon.SkuGoodInfo
	var g errgroup.Group
	if len(commonGoodIds) > 0 {
		g.Go(func() error {
			var gErr error
			commonGoods, gErr = uc.goodClient.GetAllGoodList(ctx, &clients.GetGoodListReq{
				Id: commonGoodIds,
			})
			return gErr
		})
	}
	if len(combinationGoodIds) > 0 {
		g.Go(func() error {
			var gErr error
			combinationGoods, gErr = uc.goodClient.GetAllCombinationGoodsByIds(ctx, combinationGoodIds, []string{"已上架"}, []string{common.GoodsGroupGroupBuying})
			return gErr
		})
	}
	err = g.Wait()
	if err != nil {
		return err
	}
	var specialCourseMap map[string]*clients.NewCoursePackageDetail
	specialCourseList, err := uc.courseBffExtendedClient.GetSpecialCourseListByType(ctx, &clients.GetSpecialCourseListReq{Type: "all"})
	if err != nil {
		return err
	}
	specialCourseMap = lo.SliceToMap(specialCourseList, func(item *clients.NewCoursePackageDetail) (string, *clients.NewCoursePackageDetail) {
		return item.Id, item
	})
	// 处理商品信息
	var entryGoods []*channel_model.EntryGroupBuyingGood
	goodList := lo.Map(commonGoods, func(g *good.GoodInfo, _ int) *entity.GroupGoodProcessInfo {
		return &entity.GroupGoodProcessInfo{
			Id:     g.Id,
			Name:   g.Name,
			Amount: g.Amount,
			Agent:  g.Agent,
			SkuList: lo.Map(g.SkuList, func(s *good.SKUInGood, _ int) *entity.SkuItem {
				return &entity.SkuItem{
					ExtraParams: s.ExtraParams,
					Sku:         s.Sku,
				}
			}),
		}
	})
	goodList = append(goodList, lo.Map(combinationGoods, func(g *goodCommon.SkuGoodInfo, _ int) *entity.GroupGoodProcessInfo {
		return &entity.GroupGoodProcessInfo{
			Id:     g.Id,
			Name:   g.Name,
			Amount: g.Amount,
			Agent:  &good.Agent{DiscountedPrices: cast.ToFloat32(g.Amount)},
			SkuList: lo.Map(g.SkuDetailList, func(s *goodCommon.SKUDetail, _ int) *entity.SkuItem {
				return &entity.SkuItem{
					ExtraParams: nil,
					Sku:         s,
				}
			}),
		}
	})...)
	for i := 0; i < len(goodList); i++ {
		g := goodList[i]
		goodModel, stageList, err := uc.processGoods(ctx, g, specialCourseMap, goodsNamePrefix, req.GoodsIds, req.StageList, entry.ID)
		if err != nil {
			return err
		}
		req.StageList = append(req.StageList, stageList...)
		entryGoods = append(entryGoods, goodModel)
	}
	req.StageList = lo.Filter(lo.Uniq(req.StageList), func(item string, _ int) bool {
		return item != ""
	})
	// 创建 EntryGroupBuying
	groupBuying := channel_model.EntryGroupBuying{
		EntryID:      entry.ID,
		AgencyID:     cast.ToInt64(user.AgencyID),
		StageSubject: req.StageList,
		RoomID:       req.ClassId,
		RoomName:     req.ClassName,
		RoomRef:      req.ClassRef,
		SchoolID:     cast.ToInt32(req.SchoolId),
		SchoolName:   req.SchoolName,
		Region:       room.School.RegionCode,
		TeacherID:    req.TeacherId,
		TeacherName:  req.TeacherName,
		StudentCount: req.StudentCount,
		CreatedDate:  time.Now(),
	}
	if req.StartTime != nil {
		groupBuying.ValidateDate = req.StartTime.AsTime()
	}
	if req.EndTime != nil {
		groupBuying.ExpireDate = req.EndTime.AsTime()
	}
	if req.NoticeInfo != nil {
		bys, err := json.Marshal(req.NoticeInfo)
		if err != nil {
			return err
		}
		err = groupBuying.NoticeInfo.Scan(bys)
		if err != nil {
			return err
		}
	}
	if user.IsGearbox {
		groupBuying.GearboxCancelUserID = user.ID
		groupBuying.GearboxCancelUserName = user.Name
	} else {
		groupBuying.CreateUserID = &user.ID
	}
	err = uc.groupBuyingRepo.CreateOneWithoutFields(ctx, &groupBuying, []string{"cancel_user_id", "activity_id"})
	if err != nil {
		uc.log.Errorf("创建 EntryGroupBuying 失败: %v", err)
		return errors.InternalServer("创建 EntryGroupBuying 失败", "创建 EntryGroupBuying 失败")
	}
	// 创建 EntryGroupBuyingGoods
	err = uc.groupBuyingGoodRepo.CreateBatch(ctx, entryGoods, len(entryGoods))
	if err != nil {
		uc.log.Errorf("创建 EntryGroupBuyingGoods 失败: %v", err)
		return errors.InternalServer("创建 EntryGroupBuyingGoods 失败", "创建 EntryGroupBuyingGoods 失败")
	}
	return nil
}

func (uc *EntryGroupBuyingUseCase) checkGoodsTimeRange(ctx context.Context, goodsIds []string, agencyID string) (*time.Time, *time.Time, error) {

	_, agencyGoodsList, err := uc.agencyGoodRepo.GetPaymentGoodsList(ctx, entity.PaymentGoodsListParams{
		Status:          "2",
		AgencyID:        agencyID,
		GoodsID:         goodsIds,
		VisibleType:     common.VisibleTypeVisible,
		IsForManagement: false,
	})
	if err != nil {
		uc.log.Errorf("获取代理商商品列表失败: %v", err)
		return nil, nil, errors.InternalServer("获取代理商商品列表失败", "获取代理商商品列表失败")
	}

	var goodsMinTime, goodsMaxTime *time.Time

	for _, goods := range agencyGoodsList {
		if goods.TimeRange == nil {
			continue // 永久有效商品
		}

		start, end := goods.TimeRange.Lower.Time, goods.TimeRange.Upper.Time

		if goodsMinTime == nil || start.Before(*goodsMinTime) {
			goodsMinTime = &start
		}

		if goodsMaxTime == nil || end.After(*goodsMaxTime) {
			goodsMaxTime = &end
		}
	}

	return goodsMinTime, goodsMaxTime, nil
}

func (uc *EntryGroupBuyingUseCase) processGoods(ctx context.Context, g *entity.GroupGoodProcessInfo, specialCourseMap map[string]*clients.NewCoursePackageDetail, goodsNamePrefixMap map[string]string, goodGroupPriceMap map[string]string, reqStageList []string, entryId int64) (*channel_model.EntryGroupBuyingGood, []string, error) {
	var hasStageSubjectDis *entity.SkuItem
	var firstParams entity.SkuParams
	enableSkuKinds := []string{common.SkuKindSpecialCourse, common.SkuKindVip, common.SkuKindTimingVip, common.SkuKindDelayVip, common.SkuKindTimingSpecialCourse}
	for i := 0; i < len(g.SkuList); i++ {
		sku := g.SkuList[i]
		bys, err := sku.Sku.Distributor.Params.MarshalJSON()
		if err != nil {
			return nil, nil, err
		}
		var params entity.SkuParams
		err = json.Unmarshal(bys, &params)
		if err != nil {
			return nil, nil, err
		}
		if lo.Contains(enableSkuKinds, sku.Sku.Distributor.Kind) {
			hasStageSubjectDis = sku
			firstParams = params
			break
		}
	}
	if hasStageSubjectDis == nil {
		return nil, nil, errors.Conflict("商品中需要有带有学科学段信息的sku", "商品中需要有带有学科学段信息的sku")
	}
	// common.STAGE_SUBJECT_LIST
	// 专项课有时候有多个学科学段 hasStageSubjectDis 为空，商品的 stageSubject 字段存储 ''
	stageSubject := ""
	var goodsStageSubjects []string
	if firstParams.Stage != "" && firstParams.Subject != "" {
		stageSubject = fmt.Sprintf("%s-%s", firstParams.Stage, firstParams.Subject)
		goodsStageSubjects = append(goodsStageSubjects, stageSubject)
	}
	if stageSubject == "" {
		for i := 0; i < len(g.SkuList); i++ {
			sku := g.SkuList[i]
			if sku == nil || sku.Sku.Distributor == nil {
				continue
			}
			bys, err := hasStageSubjectDis.Sku.Distributor.Params.MarshalJSON()
			if err != nil {
				return nil, nil, err
			}
			var skuParams entity.SkuParams
			err = json.Unmarshal(bys, &skuParams)
			if err != nil {
				return nil, nil, err
			}
			var curStageSubject string
			if sku.Sku.Distributor.Kind == common.SkuKindSpecialCourse || sku.Sku.Distributor.Kind == common.SkuKindTimingSpecialCourse {
				// 专项课，需要从课程包学科学段与选择学科学段交集中取一个作为商品学科学段
				course := specialCourseMap[firstParams.Id]
				courseStageSubject := uc.specialCourseStageSubject(course)
				if len(courseStageSubject) > 0 {
					goodsStageSubjects = append(goodsStageSubjects, courseStageSubject...)
				}
				list := lo.Intersect(reqStageList, courseStageSubject)
				if len(list) > 0 {
					curStageSubject = list[0]
				}
			} else {
				if firstParams.Stage != "" && firstParams.Subject != "" {
					curStageSubject = fmt.Sprintf("%s-%s", firstParams.Stage, firstParams.Subject)
					goodsStageSubjects = append(goodsStageSubjects, curStageSubject)
				}
			}
			if curStageSubject != "" && stageSubject == "" {
				stageSubject = curStageSubject
			}
		}
	}
	reqStageList = append(reqStageList, goodsStageSubjects...)
	var t string
	var addTime int64
	var endTime *time.Time
	for i := 0; i < len(g.SkuList); i++ {
		item := g.SkuList[i]
		if item == nil || item.Sku.Distributor == nil || item.Sku.Distributor.Params == nil {
			continue
		}
		bys, err := hasStageSubjectDis.Sku.Distributor.Params.MarshalJSON()
		if err != nil {
			return nil, nil, err
		}
		var skuParams entity.SkuParams
		err = json.Unmarshal(bys, &skuParams)
		if err != nil {
			return nil, nil, err
		}
		if skuParams.AddTime > 0 && addTime == 0 {
			addTime = skuParams.AddTime
		}
		if item.ExtraParams != nil && item.ExtraParams.EndTime != nil {
			if *item.ExtraParams.EndTime != "" {
				tmp, err := now.ParseInLocation(time.Local, *item.ExtraParams.EndTime)
				if err != nil {
					return nil, nil, err
				}
				endTime = &tmp
				break
			}
		}
	}
	if endTime != nil {
		t = fmt.Sprintf("%s到期", endTime.Format(common.Date))
	} else if addTime > 0 {
		t = fmt.Sprintf("%d天", addTime/1000/60/60/24)
	}
	discountedPrices, settlementPrices := g.Amount, g.Amount
	if g.Agent != nil {
		if g.Agent.DiscountedPrices > 0 {
			discountedPrices = cast.ToFloat64(g.Agent.DiscountedPrices)
		}
		if g.Agent.SettlementPrices > 0 {
			settlementPrices = cast.ToFloat64(g.Agent.SettlementPrices)
		}
	}
	entryGood := channel_model.EntryGroupBuyingGood{
		ID:                   uuid.New().String(),
		EntryID:              entryId,
		GoodsID:              g.Id,
		GoodsName:            g.Name,
		GoodsType:            "channel_group_purchase_goods",
		GoodsPay:             g.Amount,
		GoodsGroupPay:        discountedPrices,
		ChannelGoodsGroupPay: cast.ToFloat64(goodGroupPriceMap[g.Id]),
		SettlementPrices:     settlementPrices,
		Stagesubject:         stageSubject,
		Time:                 t,
		CreatedDate:          time.Now(),
		UpdatedDate:          sql.NullTime{},
		GoodsNamePrefix:      goodsNamePrefixMap[g.Id],
		IsCombination:        common.IsUUID(g.Id), // uuid类型的商品id是组合商品
	}
	return &entryGood, reqStageList, nil
}

func (uc *EntryGroupBuyingUseCase) specialCourseStageSubject(course *clients.NewCoursePackageDetail) []string {
	var res []string
	for i := 0; i < len(course.StageIds); i++ {
		stage := course.StageIds[i]
		for j := 0; j < len(course.SubjectIds); j++ {
			subject := course.SubjectIds[j]
			ss := fmt.Sprintf("%d-%d", stage, subject)
			if !lo.Contains(common.STAGE_SUBJECT_LIST, ss) {
				// 如果系统没有的课程丢弃
				continue
			}
			res = append(res, ss)
		}
	}
	return res
}

func (uc *EntryGroupBuyingUseCase) GetGroupGoods(ctx context.Context, user *token.User, schoolId int32) ([]*pb.GroupGoodItem, error) {
	// 获取代理商品列表，并过滤代理商、学校
	school, err := uc.schoolClient.GetSchool(ctx, &clients.GetSchoolReq{SchoolId: cast.ToString(schoolId)})
	if err != nil {
		return nil, err
	}
	agencyGoodsParam := entity.AgencyGoodsParams{
		AgencyIds:  []int64{cast.ToInt64(user.AgencyID)},
		Status:     "2",
		GoodsState: "已上架",
		Groups: &entity.AgencyGoodsGroupQuery{
			Logic:  "and",
			Groups: []string{common.GoodsGroupGroupBuying},
		},
		GoodsType:       []string{common.AgencyGoodTypeSku, common.AgencyGoodTypeCombination},
		SchoolIds:       []string{school.Objectid},
		VisibleType:     common.VisibleTypeVisible,
		IsForManagement: false, // 代理商使用，非管理场景
	}
	count, agencyGoodsList, err := uc.agencyGoodRepo.FindMultiByAgency(ctx, &agencyGoodsParam)
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return nil, nil
	}
	// 过滤商品、父级sku组商品、父级组合商品下架的子商品
	extendInfo, err := uc.baseUc.AgencyGoodExtendInfo(ctx, common.GoodsGroupGroupBuying, agencyGoodsList)
	if err != nil {
		return nil, err
	}
	// 组装结构
	commonGoodMap := lo.SliceToMap(extendInfo.CommonGoods, func(g *good.GoodInfo) (string, *good.GoodInfo) {
		return g.Id, g
	})
	res := lo.Map(agencyGoodsList, func(ag *channel_model.AgencyGood, _ int) *pb.GroupGoodItem {
		var item *pb.GroupGoodItem
		var timeRange []*timestamppb.Timestamp
		if ag.TimeRange != nil {
			timeRange = []*timestamppb.Timestamp{
				timestamppb.New(ag.TimeRange.Lower.Time),
				timestamppb.New(ag.TimeRange.Upper.Time),
			}
		}
		if extendInfo.CombinationGoodMap[ag.GoodsID] != nil {
			// 组合商品
			g := extendInfo.CombinationGoodMap[ag.GoodsID]
			bys, _ := json.Marshal(g.SkuDetailList)
			var skuList []*goodCommon.SKUDetail
			_ = json.Unmarshal(bys, &skuList)
			item = &pb.GroupGoodItem{
				Id:              g.Id,
				Name:            g.Name,
				GroupList:       g.GroupList,
				PaymentPlatform: g.PaymentPlatform,
				SkuList: lo.Map(skuList, func(sku *goodCommon.SKUDetail, _ int) *pb.SKUInGood {
					return &pb.SKUInGood{
						Amount: cast.ToFloat64(sku.Amount),
						SkuId:  sku.Id,
						Sku:    sku,
						Kind:   sku.Distributor.Kind,
					}
				}),
				Amount:             g.Amount,
				Status:             g.Status,
				Agent:              &pb.Agent{DiscountedPrices: g.Amount},
				TimeRange:          timeRange,
				GoodsId:            g.Id,
				GoodsName:          g.Name,
				GoodsType:          strings.Join(g.GroupList, ","),
				GoodsPay:           fmt.Sprintf("￥ %.2f", g.Amount),
				GoodsGroupPay:      fmt.Sprintf("￥ %.2f", g.Amount),
				SettlementPrices:   "--",
				GoodsAmount:        fmt.Sprintf("%.2f", g.Amount),
				IsCombinationGood:  true,
				GoodsIsGroup:       "是",
				RequiredItemList:   g.RequiredItemList,
				MandatoryItemList:  g.MandatoryItemList,
				AdditionalItemList: g.AdditionalItemList,
			}
		} else if commonGoodMap[ag.GoodsID] != nil {
			// 普通商品
			g := commonGoodMap[ag.GoodsID]
			var discountPrice, settlementPrice float32
			if g.Agent != nil {
				discountPrice, settlementPrice = g.Agent.DiscountedPrices, g.Agent.SettlementPrices
			}
			bys, _ := json.Marshal(g.SkuList)
			var skuList []*pb.SKUInGood
			_ = json.Unmarshal(bys, &skuList)
			item = &pb.GroupGoodItem{
				Id:                g.Id,
				Name:              g.Name,
				GroupList:         g.GroupList,
				SkuGroupGoodId:    g.SkuGroupGoodId,
				PaymentPlatform:   g.PaymentPlatform,
				SkuList:           skuList,
				Amount:            g.Amount,
				Status:            g.Status,
				Agent:             &pb.Agent{DiscountedPrices: cast.ToFloat64(discountPrice), SettlementPrices: cast.ToFloat64(settlementPrice)},
				Description:       g.Description,
				TimeRange:         timeRange,
				GoodsId:           g.Id,
				GoodsName:         g.Name,
				GoodsType:         strings.Join(g.GroupList, ","),
				GoodsPay:          lo.If(g.OriginalAmount > 0, fmt.Sprintf("￥ %.2f", g.OriginalAmount)).Else("--"),
				GoodsIsGroup:      "是",
				GoodsGroupPay:     lo.If(discountPrice > 0, fmt.Sprintf("￥ %.2f", discountPrice)).ElseIf(g.SkuGroupGoodId != "", fmt.Sprintf("￥ %.2f", g.Amount)).Else("--"),
				SettlementPrices:  lo.If(settlementPrice > 0, fmt.Sprintf("%.2f", settlementPrice)).Else("--"),
				GoodsAmount:       cast.ToString(g.Amount),
				IsCombinationGood: false,
			}
		}
		return item
	})
	res = lo.WithoutEmpty(res)
	// 获取专项课详情，将商品中专项课sku根据学科学段转换为多个sku
	pkgSSMap, err := uc.specialCoursePkgRepo.FindMultiPkgStageSubject(ctx)
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(res); i++ {
		g := res[i]
		var skuList []*pb.SKUInGood
		for j := 0; j < len(g.SkuList); j++ {
			sku := g.SkuList[j]
			newSkus, err := uc.splitSpecialCourseSkuByStageSubject(sku, pkgSSMap)
			if err != nil {
				return nil, err
			}
			skuList = append(skuList, newSkus...)
		}
		res[i].SkuList = skuList
	}
	return res, nil
}

func (uc *EntryGroupBuyingUseCase) splitSpecialCourseSkuByStageSubject(sku *pb.SKUInGood, pkgSSMap map[string]*entity.SpecialCourseStageSubjects) ([]*pb.SKUInGood, error) {
	if !common.SpecialCourseKinds()[sku.Sku.Distributor.Kind] {
		// 非专项课
		return []*pb.SKUInGood{sku}, nil
	}
	// 专项课
	var p entity.SkuParams
	bys, err := protojson.Marshal(sku.Sku.Distributor.Params)
	if err != nil {
		return nil, err
	}
	if err = json.Unmarshal(bys, &p); err != nil {
		return nil, err
	}
	ss := pkgSSMap[p.Id]
	if ss == nil || len(ss.StageIds) == 0 || len(ss.SubjectIds) == 0 {
		return []*pb.SKUInGood{sku}, nil
	}
	var list []*pb.SKUInGood
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	for i := 0; i < len(ss.StageIds); i++ {
		stage := ss.StageIds[i]
		for j := 0; j < len(ss.SubjectIds); j++ {
			subject := ss.SubjectIds[j]
			var newSku pb.SKUInGood
			if err := copier.Copy(&newSku, sku); err != nil {
				return nil, err
			}
			var param entity.SkuParams
			bys, err = protojson.Marshal(sku.Sku.Distributor.Params)
			if err != nil {
				return nil, err
			}
			if err = json.Unmarshal(bys, &param); err != nil {
				return nil, err
			}
			param.Stage = cast.ToString(stage)
			param.Subject = cast.ToString(subject)
			bys, err = json.Marshal(param)
			if err != nil {
				return nil, err
			}
			err = unmarshaler.Unmarshal(bys, newSku.Sku.Distributor.Params)
			if err != nil {
				return nil, err
			}
			list = append(list, &newSku)
		}
	}
	return list, err
}
