package iface

import (
	"context"

	"time"

	pb "gitlab.yc345.tv/backend/channel/api/entry"
	orderPb "gitlab.yc345.tv/backend/channel/api/order"
	prepayPb "gitlab.yc345.tv/backend/channel/api/prepay"
	"gitlab.yc345.tv/backend/channel/clients"
	de "gitlab.yc345.tv/backend/channel/internal/biz/domain/entity"
	"gitlab.yc345.tv/backend/channel/internal/biz/entity"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_dao"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_model"
	"gitlab.yc345.tv/backend/channel/internal/data/gorm/channel_repo"
	"gitlab.yc345.tv/backend/channel/internal/data/model"
	"gitlab.yc345.tv/backend/channel/internal/pkg/middleware/token"
)

type IShopCourseLinkRepo interface {
	channel_repo.IShopCourseLinkRepo
	GetLinks(ctx context.Context, params entity.GetLinksParams) ([]*channel_model.ShopCourseLink, int64, error)
	// GetShopCourseLinkByID(ctx context.Context, id int64) (*channel_model.ShopCourseLink, error)
	// CreateShopCourseLink(ctx context.Context, link *channel_model.ShopCourseLink) error
	// UpdateShopCourseLink(ctx context.Context, link *channel_model.ShopCourseLink) error
	// DeleteShopCourseLink(ctx context.Context, id int64) error
	// ListShopCourseLinks(ctx context.Context, cond entity.ListShopCourseLinksCond) ([]*channel_model.ShopCourseLink, int64, error)
	FindOneByCourseIDOrLink(ctx context.Context, courseID, link string) (*channel_model.ShopCourseLink, error)
	FindChildLinks(ctx context.Context, parentID, createdUserID string) ([]*channel_model.ShopCourseLink, error)
	DeleteShopCourseLink(ctx context.Context, id, userId string, agencyID int32) error
}

type IAgencyRepo interface {
	channel_repo.IAgencyRepo
	GetSchoolEmployees(ctx context.Context, schoolIds []string, agencyIds []int64) ([]*channel_model.AgencyEmployeeSchool, error)
	GetAgenciesByIDs(ctx context.Context, agencyIDs []int64) ([]*channel_model.Agency, error)
	GetAgency(ctx context.Context, agencyID string) (channel_model.Agency, error)
	GetAgencyRegion(ctx context.Context, agencyID string) (channel_model.AgencyContractRegion, error)
	GetAgencySuperVise(ctx context.Context, regionCode string) (model.Regions, error)
	GetAgencyByIDs(ctx context.Context, agencyIDs []string) ([]channel_model.Agency, error)
	GetAgenciesByOptions(ctx context.Context, opt *entity.GetAgenciesByOptions) (int64, []*entity.AgencyWithAdminUser, error)
}
type IUserRepo interface {
	// FindOne TODO 此处可做内存缓存
	channel_repo.IUserRepo
	FindOne(ctx context.Context, userID string) (*channel_model.User, error)
	FindUserRegions(ctx context.Context, userID string) ([]*channel_model.Region, error)
	GetManagerList(ctx context.Context) ([]*channel_model.User, error)
	// TODO GetManagersByRegions(ctx context.Context, regions []string) (res []entity.UserManager, err error)
	UpdateUser(ctx context.Context, info channel_model.User) error
	GetUserByID(ctx context.Context, id string) (*channel_model.User, error)
	GetUsersByAgencyID(ctx context.Context, agencyID int32) ([]*channel_model.User, error)
}
type IContractRepo interface {
	// FindAllContractRegions 获取所有的合同区域，agencyID为0不查询，excludeAgencyID为排除的���理商id，eqRegions为等于区域，likeRegions为like区域，isFeiDuJia为nil时不限制签约方式
	channel_repo.IAgencyContractRepo
	FindAllContractRegions(ctx context.Context, agencyID, excludeAgencyID int64, eqRegions, likeRegions []string, isFeiDuJia *bool, isDuJia *bool) ([]*channel_model.AgencyContractRegion, error)
	GetAgenciesBySchoolID(ctx context.Context) ([]*channel_model.AgencyContract, error)
	GetContractsByAgencyIdAndTime(ctx context.Context, agencyId int64, time time.Time) ([]*channel_model.AgencyContract, error)
}

type IMaintainSchoolRepo interface {
	// FindMaintainSchoolsByCodes TODO 可使用redis缓存
	channel_repo.IMaintainSchoolRepo
	FindMaintainSchoolsByCodes(ctx context.Context, shortCodes []int32) ([]channel_model.MaintainSchool, error)
	FindMaintainSchoolsByPage(ctx context.Context, adminSchools de.SchoolRange, adminRegions de.RegionsRange, excludeSchoolsCodes []int32, limit, offset *int32) (int, []channel_model.MaintainSchool, error)
	// TODO List(ctx context.Context, params entity.GetSchoolListParams) (int64, []channel_model.MaintainSchool, error)
	Update(ctx context.Context, school channel_model.MaintainSchool) error
	GetSchoolByID(ctx context.Context, schoolID string) (channel_model.MaintainSchool, error)
	HasAgency(ctx context.Context, region string) (bool, error)
	GetList(ctx context.Context, offset, limit int) (int, []*model.MaintainSchool, error)
	UpdateBaseInfo(ctx context.Context, id int, name, regionCode string, stageID int32) error
}

type IAgencyEmployeeSchoolRepo interface {
	channel_repo.IAgencyEmployeeSchoolRepo
	FindAllEmployeeSchoolsByUser(ctx context.Context, userID string, agencyID int64, shortCodes []string) ([]channel_model.AgencyEmployeeSchool, error)
	List(ctx context.Context, agencyID int, employeeID string) ([]model.AgencyEmployeeSchool, error)
}

// IEntryRepo 工单
type IEntryRepo interface {
	channel_repo.IEntryRepo
	SetEntryState(ctx context.Context, entryID, state int32, reason string) error
	FindSchoolsByOpMgr(ctxt context.Context, userID string) ([]channel_model.EntryAgencySchoolOrder, error)
	//  TODO GetEntryOrderList(ctx context.Context, params entity.EntryOrderParam) (int64, []*channel_model.EntryAgencySchoolOrder, error)
	GetEntryState(ctx context.Context, entryID string) (*model.Entry, error)
	CreateEntryTeacherTrialVipByTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.Entry) error
	UpdateEntryStateByTx(ctx context.Context, tx *channel_dao.Query, entryID int64, state int32) error // 修改这一行
	GetTotoListByCache(ctx context.Context, userId string) ([]*pb.ToDoItem, error)
	CacheTodoList(ctx context.Context, userId string, todoList []*pb.ToDoItem) error
	CreateOneWithoutFields(ctx context.Context, data *channel_model.Entry, fields []string) error
}

type IRegionRepo interface {
	channel_repo.IRegionRepo
	// TODO GetAllRegionsFromCache(ctx context.Context) (map[string]*clients.GetAllRegionsReply_Nested, error)
}

type IAgencyAreaSchoolRepo interface {
	channel_repo.IAgencyAreaSchoolRepo
	GetAgencySchoolById(ctx context.Context, schoolID string) (channel_model.AgencyAreaSchool, error)
}

type IUserSearchLogRepo interface {
	channel_repo.ISearchUserLogRepo
	// GetUserAboutChannel(ctx context.Context, userID string, operateUser token.User) (*entity.UserAboutChannel, error)
	CreateLog(ctx context.Context, operateUser token.User, userId, operationType string) error
}

type IAuthRepo interface {
	channel_repo.IAuthRepo
	CheckToken(ctx context.Context, token string) (user *entity.ChannelClaims, newToken string, err error)
}

type IAuthDictionaryRepo interface {
	channel_repo.IAuthDictionaryRepo
	HasAuthInUser(ctx context.Context, userID string, authKeys []string) ([]*channel_model.Auth, error)
}

type IEntryTenantAuthRepo interface {
	channel_repo.IEntryTenantAuthRepo
	GetTenantAuthRemainingDays(ctx context.Context, schoolID string) ([]*channel_model.EntryTenantAuth, error)
	CreateTenantAuthByTx(ctx context.Context, tx *channel_dao.Query, tenantAuth *channel_model.EntryTenantAuth) (*channel_model.EntryTenantAuth, error)
	GetListBySchoolId(ctx context.Context, schoolId string) ([]*channel_model.EntryTenantAuth, error)
	GetEntryTenantAuthList(ctx context.Context, params *entity.GetTenantAuthListParams) (int64, []*channel_model.EntryTenantAuth, error)
	UpdateAuthorizationResultByTx(ctx context.Context, tx *channel_dao.Query, entryID int64, authResults []interface{}) error // 修改这一行
}

type IBusinessOrderRepo interface {
	// channel_repo.IBusinessOrderRepo
	GetListBySchoolId(ctx context.Context, schoolId string) ([]*channel_model.BusinessOrder, error)
}

type IEntryGroupBuyingRepo interface {
	channel_repo.IEntryGroupBuyingRepo
	GetGroupBuyingListByParams(ctx context.Context, params *entity.GroupBuyingListParam) (int64, []*channel_model.EntryGroupBuying, error)
	FindOneWithGoodsByEntryId(ctx context.Context, entryId int64) (*channel_model.EntryGroupBuying, error)
	GetGroupBuyCountOneYear(ctx context.Context, roomRef string, stageSubjects []string, agencyId int64) (int64, error)
	UpdateGroupBuy(ctx context.Context, groupBuy *channel_model.EntryGroupBuying, updateUser *token.User, state int32) error
	CreateOneWithoutFields(ctx context.Context, data *channel_model.EntryGroupBuying, fields []string) error
}

type IEntryGroupBuyingGoodRepo interface {
	channel_repo.IEntryGroupBuyingGoodRepo
	CreateOneWithoutFields(ctx context.Context, data *channel_model.EntryGroupBuyingGood, fields []string) error
	CreateBatchWithoutFields(ctx context.Context, data []*channel_model.EntryGroupBuyingGood, batchSize int, fields []string) error
}

type IThirdPartRepo interface {
	GetRoomByRef(ctx context.Context, ref string) (*entity.RoomWithSchool, error)
	GetRegionTreeName(ctx context.Context, regionCode string) (*entity.RegionDetail, error)
	GetRegionTree(ctx context.Context) ([]*clients.ProvinceTree, error)
	GetRegionTreeWithAgencyArea(ctx context.Context) ([]*clients.ProvinceTree, error)
	GetStudyTourShowStatusByCache(ctx context.Context, userID string) (bool, error)
	CacheStudyTourShowStatus(ctx context.Context, userID string, isShow bool) error
	GetMaintainSchoolsByAgencyCache(ctx context.Context, agencyID int32, fn func() ([]*entity.CacheMaintainSchool, error)) ([]*entity.CacheMaintainSchool, error)
}

type IEntryLogRepo interface {
	channel_repo.IEntryLogRepo
}

type IEntryTrialVipRepo interface {
	channel_repo.IEntryTrialVipRepo
	GetActiveTrialVipByRefs(ctx context.Context, refs []string) ([]model.EntryTrialVips, error)
	GetVipList(ctx context.Context, cond entity.GetVipListCond) (int64, []*channel_model.EntryTrialVip, error)
	GetTRoomCount(ctx context.Context, cond entity.GetVipListCond) (int64, error)
	GetAgencySumCount(ctx context.Context, cond entity.GetVipListCond) (int64, error)
	GetTrialVipByEntryID(ctx context.Context, entryID int64) (*channel_model.EntryTrialVip, error)
}

type IEntryFomalVipRepo interface {
	channel_repo.IEntryFomalVipRepo
}

type IEntrySchoolRepo interface {
	channel_repo.IEntrySchoolRepo
}

type IEntryPrepayRepo interface {
	channel_repo.IEntryPrepayRepo
}

type IEntryMaintainSchoolUndoRepo interface {
	channel_repo.IEntryMaintainSchoolUndoRepo
}

type IAgencyGoodRepo interface {
	channel_repo.IAgencyGoodRepo
	GetPaymentGoodsList(ctx context.Context, params entity.PaymentGoodsListParams) (int, []*channel_model.AgencyGood, error)
	FindMultiByAgency(ctx context.Context, param *entity.AgencyGoodsParams) (int64, []*channel_model.AgencyGood, error)
	UpdateStatusByGoodIds(ctx context.Context, goodIds []string, status string) error
	UpdateGroupsByGoodId(ctx context.Context, goodId string, groups []string) error
}

type IAgencyContractRegionRepo interface {
	channel_repo.IAgencyContractRegionRepo
	FindListWithContractBySchool(ctx context.Context, agencyId, schoolId int32, regionCode string) ([]*channel_model.AgencyContractRegion, error)
}

type ISpecialCoursePkgRepo interface {
	FindMultiPkgStageSubject(ctx context.Context) (map[string]*entity.SpecialCourseStageSubjects, error)
}

type IRedisLockRepo interface {
	Lock(ctx context.Context, key, val string, expire time.Duration) (bool, error)
	LockRelease(ctx context.Context, key string) error
}

type IAgencyPaymentRepo interface {
	channel_repo.IAgencyPaymentRepo
	Find(ctx context.Context, tradeID string) (*model.AgencyPayment, error)
	FindRecentAgencyGoods(ctx context.Context, isBigMember bool, agencyID int64, schoolID string, limit int) (rows []*entity.AgencyPaymentGood, err error)
	FindAgencyGoodsByUserID(ctx context.Context, userID string) (rows []*entity.AgencyPaymentGood, err error)
	CreatePrepayOrder(ctx context.Context, payment *channel_model.AgencyPayment, paymentStudents []*channel_model.AgencyPaymentStudent, paymentParams *clients.CreateChannelOrderReq) (*channel_model.AgencyPayment, *prepayPb.TransactionDetail, error)
	GetAgencyPaymentList(ctx context.Context, conditions *entity.AgencyPaymentListConditions) (*entity.AgencyPaymentListResult, error)
	GetRefundList(ctx context.Context, conditions *entity.RefundListConditions) (*entity.RefundListResult, error)
}

type IAgencyPaymentStudentRepo interface {
	channel_repo.IAgencyPaymentStudentRepo
	ListByPage(ctx context.Context, agencyID int, schoolIDs []string, payID string, params *orderPb.AgencyPaymentOrdersReq) (count int64, rows []model.AgencyPaymentStudent, err error)
	SetLogistics(ctx context.Context, orderIDs []string) error
	Find(ctx context.Context, orderIDs []string) ([]model.AgencyPaymentStudent, error)
	GetOrderIdsByTransactionId(ctx context.Context, transactionId string) ([]string, error)
	UpdateOneByPayIDStudentIDTx(ctx context.Context, tx *channel_dao.Query, data *channel_model.AgencyPaymentStudent) error
	UpdateOneByPayIDStudentID(ctx context.Context, data *channel_model.AgencyPaymentStudent) error
}
