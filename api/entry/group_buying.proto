syntax = "proto3";

package entry;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/any.proto";
import "protoc-gen-openapiv2/options/annotations.proto";
import "openapiv3/annotations.proto";
import "api/entry/base.proto";
import "google/protobuf/empty.proto";
import "good/common/common.proto";


option go_package = "gitlab.yc345.tv/backend/channel/api/entry;entry";

// 概要
option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_swagger) = {
  info: {
    title: "entry"
    version : "1.0.0"
    description: "channel系统"
  };
  host: "channel.teacherschool"
  schemes: HTTP
  consumes: ""
  produces: ""
};

service GroupBuying {
  rpc GetGroupBuyList (GetGroupBuyListReq) returns (GetGroupBuyListRes) {
    option (google.api.http) = {
      get: "/channel/entry/group-buying-list"
    };
    option (openapi.v3.operation) = {
      summary : "获取团购列表",
      tags: "班级团购",
    };
  }
  rpc GetGroupBuy (EntryIdReq) returns (GroupBuyingDetail) {
    option (google.api.http) = {
      get: "/channel/entry/group-buying-list/{entryId}"
    };
    option (openapi.v3.operation) = {
      summary : "团购详情",
      tags: "班级团购",
    };
  }
  rpc CancelGroupBuy (EntryIdReq) returns (CancelGroupBuyRes) {
    option (google.api.http) = {
      delete: "/channel/entry/group-buying-list/{entryId}"
    };
    option (openapi.v3.operation) = {
      summary : "团购终止",
      tags: "班级团购",
    };
  }
  rpc DecideGroupBuy (DecideGroupBuyReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/channel/entry/group-buying-list/{entryId}"
      body: "*"
    };
    option (openapi.v3.operation) = {
      summary : "审核团购",
      tags: "班级团购",
    };
  }
  rpc CreateGroupBuy (CreateGroupBuyReq) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/channel/entry/group-buying"
      body: "*"
    };
    option (openapi.v3.operation) = {
      summary : "创建团购",
      tags: "班级团购",
    };
  }
  rpc GetGroupGoods (GetGroupGoodsReq) returns (GetGroupGoodsRes) {
    option (google.api.http) = {
      get: "/channel/entry/group-buying/goods"
    };
    option (openapi.v3.operation) = {
      summary : "团购商品",
      tags: "班级团购",
    };
  }
}
message GetGroupBuyListReq {
  // 省级区域码.
  string provinceCode = 1;
  // 市级区域码.
  string cityCode = 2;
  // 区级区域码.
  string countyCode = 3;
  // 工单id.
  int64 entryId = 4;
  // 班级编号.
  string roomRef = 5;
  // 学校id（短id）.
  int32 schoolId = 6;
  // 开始时间，格式：2024-09-01.
  string validateDate = 7;
  // 结束时间，格式：2024-09-04.
  string expireDate = 8;
  // 状态，pre_check：待审核、pre_start：未开始、starting：进行中、fail：审核未通过、end：已结束、expire：已过期、cancel：已终止.
  string state = 9;
  // 学科学段.
  string stageSubject = 10 [json_name = "stage_subject"];
  int32 limit = 11;
  int32 offset = 12;
  string beforeBeginDate = 13;
  string phone = 14;
  string  sort = 15;
}

message GetGroupBuyListRes {
  uint32 total = 1;
  repeated GroupBuyItem groupBuyList = 2;
}
message GroupBuyItem {
  string entryId = 1 [json_name = "entry_id"];
  google.protobuf.Timestamp validateDate = 2 [json_name = "validate_date"];
  google.protobuf.Timestamp expireDate = 3 [json_name = "expire_date"];
  repeated string stageSubject = 4 [json_name = "stage_subject"];
  string roomId = 5 [json_name = "room_id"];
  string roomRef = 6 [json_name = "room_ref"];
  string roomName = 7 [json_name = "room_name"];
  repeated string uids = 8;
  int32 schoolId = 9 [json_name = "school_id"];
  string schoolName = 10 [json_name = "school_name"];
  string region = 11;
  string teacherId = 12 [json_name = "teacher_id"];
  string teacherName = 13 [json_name = "teacher_name"];
  int32 studentCount = 14 [json_name = "student_count"];
  string agencyId = 15 [json_name = "agency_id"];
  string activityId = 16;
  string createUserId = 17 [json_name = "create_user_id"];
  //string cancelResult = 18 [json_name = "cancel_result"];
  string cancelUserId = 19 [json_name = "cancel_user_id"];
  string gearboxCancelUserId = 20 [json_name = "gearbox_cancel_user_id"];
  string gearboxCancelUserName = 21 [json_name = "gearbox_cancel_user_name"];
  google.protobuf.Timestamp cancelDate = 22 [json_name = "cancel_date"];
  string cancelRemark = 23;
  NoticeInfo noticeInfo = 24;
  google.protobuf.Timestamp createdDate = 25 [json_name = "created_date"];
  Agency agency = 26;
  EntryInfo detail = 27;
}

message CancelGroupBuyReq {
  int32 entryId = 1;
}

message CancelGroupBuyRes {
  google.protobuf.Empty ret1 = 1;
  repeated int32 ret2 = 2;
}
message NoticeInfo {
  Condition condition = 1;
  Template template = 2;
  Time time = 3;
}
message Condition {
  string beforeVipEnd = 1;
}
message Template {
  string title = 1;
  string description = 2;
}
message Time {

}

message GroupBuyingDetail {
  string entryId = 1;
  string roomId = 2;
  string roomRef = 3;
  string roomName = 4;
  string cancelDate = 5 [json_name = "cancel_date"];
  string cancel_user_name = 6 [json_name = ""];
  int32 studentCount = 7;
  string agencyId = 8;
  int32 schoolId = 9;
  string schoolName = 10;
  int32 stageId = 11;
  string schoolRegion = 12;
  string county = 13;
  string city = 14;
  string date = 15;
  repeated string stageSubject = 16;
  string creator = 17;
  string agencyName = 18;
  //  repeated string agencyLevel = 19;
  //  repeated string agencyStageSubject = 20;
  int32 roomGroupBuyCount = 21;
  int32 agencyGroupBuyCount = 22;
  string createdDate = 23;
  string expire_date = 24 [json_name = ""];
  string remark = 25;
  repeated GroupBuyingGoods goods = 26;
  NoticeInfo noticeInfo  = 27;
}

message GroupBuyingGoods {
  string id = 1;
  string entryId = 2;
  string goodsId = 3;
  string goodsName = 4;
  string goodsNamePrefix = 5;
  string goodsType = 6;
  string goodsPay = 7;
  string goodsGroupPay = 8;
  string channelGoodsGroupPay = 9;
  string settlementPrices = 10;
  string stageSubject = 11;
  string time = 12;
  string createDate = 13;
  string updateDate = 14;
  string created_date = 15;
  string entryIdBak = 16 [json_name = "entry_id"];
  // [必须]的SKU条目列表
  repeated common.SkuGoodItem requiredItemList = 17;
  // [必选]的SKU条目列表
  repeated common.SkuGoodItem mandatoryItemList = 18;
  // [加购]的SKU条目列表
  repeated common.SkuGoodItem additionalItemList = 19;
}

message DecideGroupBuyReq {
  int32 entryId = 1;
  int32 state = 2;
  string rejectRemark = 3;
}

message CreateGroupBuyReq {
  string classRef = 1;
  repeated string stageList = 2;
  string remark = 3;
  repeated string title = 4;
  map<string, string> goodsIds = 5;
  string description = 6;
  map<string, string> goodsNamePrefix = 7;
  google.protobuf.Timestamp startTime = 8;
  google.protobuf.Timestamp endTime = 9;
  string creator = 10;
  string schoolName = 11;
  int64 schoolId = 12;
  string regionCode = 13;
  NoticeInfo noticeInfo = 14;
  string className = 15;
  string classId = 16;
  int32 studentCount = 17;
  string teacherName = 18;
  string teacherId = 19;
}

message GetGroupGoodsReq {
  string full = 1;
  int32 schoolId = 2;
}

message GroupGoodItem {
  string id = 1;
  string name = 2;
  // 商品分组列表.
  repeated string groupList = 3;
  // sku组商品id.
  string skuGroupGoodId = 4;
  // 支付平台.
  repeated string paymentPlatform = 5;
  // sku列表.
  repeated SKUInGood skuList = 6;
  // 金额.
  double amount = 7;
  // 状态.
  string status = 8;
  // 代理价格.
  optional Agent agent = 9;
  string description = 10;
  repeated google.protobuf.Timestamp timeRange = 11;
  string goodsId = 12;
  string goodsName = 13;
  string goodsType = 14;
  string goodsPay = 15;
  string goodsGroupPay = 16;
  string settlementPrices = 17;
  string goodsAmount = 19;
  // 是否组合商品.
  bool isCombinationGood = 18;
  string goodsIsGroup = 20;
  // [必须]的SKU条目列表
  repeated common.SkuGoodItem requiredItemList = 21;
  // [必选]的SKU条目列表
  repeated common.SkuGoodItem mandatoryItemList = 22;
  // [加购]的SKU条目列表
  repeated common.SkuGoodItem additionalItemList = 23;
}

message GetGroupGoodsRes {
  repeated GroupGoodItem rows = 1;
}

// SKUInGood SKU在商品中的信息.
message SKUInGood {
  double amount = 1;
  int32 pointsAmount = 2;
  string skuId = 3;
  optional ExtraParams extraParams = 4;
  common.SKUDetail sku = 5;
  bool isGift = 6;
  string kind = 7;
  bool isJiagou = 8;
}
// ExtraParams sku加入商品时添加的额外参数
message ExtraParams {
  optional float suggestPrice = 1; // 建议价格
  optional string startTime  = 2; // 专项课的开课时间
  optional string endTime  = 3; // 到期型商品、专项课的到期时间
  optional string curriculumId = 4; // （小学小班）课程ID
  optional int32 checkDelay = 5; // 延迟生效型sku的最迟确认delay，单位（天）
}

// Agent 代理商价格
message Agent {
  double discountedPrices = 1;
  double settlementPrices = 2;
}