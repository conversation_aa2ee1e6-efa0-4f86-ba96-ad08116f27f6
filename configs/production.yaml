yc:
  redis:
    channel:
      name: channel
      addr: redis-cnlfmqs62v4wrvrha.redis.ivolces.com:6379 #todo 修改实际使用redis地址
      password: "Yangcong345"
      db: 0
      keyPrefix: "channel:"
      poolSize: 294
      minIdleConns: 235
  pg:
    channel:
      name: channel
      host: yc-channel.rds-pg.ivolces.com
      port: 5432
      user: channel
      password: United_cha
      db: channel
      sslMode: disable
      maxIdleConns: 5
      maxOpenConns: 10
      connMaxLifetime: 60s
      debug: true
      singularTable: true
      disableMetric: false
      slowThreshold: 0.5s
    channeldata:
      name: channel-data
      host: yc-channel-data.rds-pg.ivolces.com
      port: 5432
      user: channel
      password: United_cha
      db: channel_data
      sslMode: disable
      maxIdleConns: 5
      maxOpenConns: 10
      connMaxLifetime: 60s
      debug: true
      singularTable: true
      disableMetric: false
      slowThreshold: 0.5s
  nacos:
    host: ${NACOS_SD_HOST}
    port: ${NACOS_SD_PORT}
    username: ${NACOS_SD_ACCOUNT}
    password: ${NACOS_SD_PASSWD}
  phoneSecretKey: "f3d746f00d0945c3a6edb9dc21f4b91f"


ratelimit:
  window: 10
  bucket: 100
  cpuThreshold: 800
yapiProjectId: "1234"

ycoss:
  accessToken: "ffd46e4e-b0a6-45aa-b407-d92a86adfa9b"
  bucket: "school-res"
  domain: "https://school-res.yangcong345.com"

feishu:
  approvalCode: "AC657394-B27B-4BB4-8F80-73B643A03F21"
  invoiceApprovalCode: "354BF08C-12FF-4E21-8BD0-6EE0CC3EB771"
  entryInvoiceApprovalCode: "06C78BDE-5C2F-4BC5-8C31-8316C367914A"
  offlineOrderApprovalCode: "EBC562E9-4574-45A7-8F22-AC5B33AF1DC3"
  userId: "77a7fc5g"
  openId: "ou_00c5f9697cf4f70bc31e90495f66c404"
  departmentId: "69d12g579df5db45"
  clientId: "microServiceKey-pro"
  clientSecret: "microService-gearbox-pro"
  agencyUserAlert: "https://open.feishu.cn/open-apis/bot/v2/hook/76ad392d-9b43-48e5-a12b-204870198df9"
jwt:
  secret: "IVawunxdozVHiqS7MME22"
  shadowSecret: "yddadruoysohw"
  expiresIn: "720h"

cronTasks:
  notify: FEISHU # 通知方式,目前支持FEISHU,CONSOLE,如果配置了该参数,代表下面的任务全部是根据该参数进行通知
  feishu: # 飞书通知配置
    url: https://open.feishu.cn/open-apis/bot/v2/hook/701cab2f-43c1-4061-9172-22e474c2adb8 # 飞书通知url
    title: 定时任务 # 飞书通知标题
  task1:
    name: customer_event # 任务1的名称,需要保证唯一
    cron: "0 0 0 20 * *" # 任务1的定时周期
    mode: default
  refreshGood:
    name: refreshGood # 任务1的名称,需要保证唯一
    cron: "0 */10 * * * *" # 任务1的定时周期
    mode: skip # 运行模式,default:到时间就开一个新的协程处理任务; skip:该执行的时候,上一个还没结束,就跳过本次运行; delay:该执行的时候,上一个还没结束,就等着,直到上一个执行完后续继续执行
hiddenStudyTourAgency: 1574 # 隐藏研学的代理商id
goodKindIdLevel2For998:
  - "9eb79b68-99f2-4bd0-a9c7-061af50a186a"
  - "27fc5116-263b-4ee6-9b56-b71029bead61"
  - "14cd8784-5583-48a6-a14b-85dfc63a2848"
  - "0d63071c-a690-4b51-ba2d-c9387c69026c"
  - "5e42f66c-0376-41b6-860b-9e437662283a"
