yc:
  redis:
    channel:
      name: channel
      addr: **********:6379 #todo 修改实际使用redis地址
      password:
      db: 4
      keyPrefix: "channel:"
  pg:
    channel:
      name: channel
      host: **********
      port: 5436
      user: postgres
      password: teacherschoolpg94
      db: channel
      sslMode: disable
      maxIdleConns: 5
      maxOpenConns: 10
      connMaxLifetime: 60s
      debug: true
      singularTable: true
      disableMetric: false
      slowThreshold: 0.5s
    channeldata:
      name: channel-data
      host: **********
      port: 5436
      user: postgres
      password: teacherschoolpg94
      db: channel_data
      sslMode: disable
      maxIdleConns: 5
      maxOpenConns: 10
      connMaxLifetime: 60s
      debug: true
      singularTable: true
      disableMetric: false
      slowThreshold: 0.5s
  nacos:
    host: nacos.ops
    port: 8848
    username: nacos
    password: unitednac

ratelimit:
  window: 10
  bucket: 100
  cpuThreshold: 800
yapiProjectId: "1234"

ycoss:
  accessToken: "dfd17283-2e23-46bb-9762-92a959ebba7e"
  bucket: "school-res"
  domain: "https://school-res.yangcong345.com"

feishu:
  approvalCode: "67F7CFCD-4BA2-40A3-8A55-B476B2270FCC"
  invoiceApprovalCode: "DA0AE845-1851-427D-BB32-20556E9884D2"
  entryInvoiceApprovalCode: "716B6408-9288-4502-924C-CF804EA5F9F2"
  offlineOrderApprovalCode: "959E536C-A1ED-49BA-A324-36933F03A86F"
  userId: "77a7fc5g"
  openId: "ou_00c5f9697cf4f70bc31e90495f66c404"
  departmentId: "69d12g579df5db45"
  clientId: "microServiceKey-dev"
  clientSecret: "microService-gearbox-dev"
  agencyUserAlert: "https://open.feishu.cn/open-apis/bot/v2/hook/750dfda6-2f6a-46be-9ce0-902dd963db0f"
jwt:
  secret: "IVawunxdozVHiqS7MME22"
  shadowSecret: "super MUZH"
  expiresIn: "720h"

cronTasks:
  notify: FEISHU # 通知方式,目前支持FEISHU,CONSOLE,如果配置了该参数,代表下面的任务全部是根据该参数进行通知
  feishu: # 飞书通知配置
    url: https://open.feishu.cn/open-apis/bot/v2/hook/fe27e07d-cb82-42a6-baaa-6bb5cacaecf7 # 飞书通知url
    title: 定时任务 # 飞书通知标题
  task1:
    name: customer_event # 任务1的名称,需要保证唯一
    cron: "0 0 0 20 * *" # 任务1的定时周期
    mode: default # 运行模式,default:到时间就开一个新的协程处理任务; skip:该执行的时候,上一个还没结束,就跳过本次运行; delay:该执行的时候,上一个还没结束,就等着,直到上一个执行完后续继续执行
  refreshGood:
    name: refreshGood # 任务1的名称,需要保证唯一
    cron: "0 */10 * * * *" # 任务1的定时周期
    mode: skip # 运行模式,default:到时间就开一个新的协程处理任务; skip:该执行的时候,上一个还没结束,就跳过本次运行; delay:该执行的时候,上一个还没结束,就等着,直到上一个执行完后续继续执行
hiddenStudyTourAgency: 291 # 隐藏研学的代理商id
goodKindIdLevel2For998:
  - "967e062e-91c3-4ac6-b3bd-e4a244a1d2ff"
  - "cdab8fb8-3c51-41de-9887-1f98fa5b7615"
  - "2bcf338e-5dc5-4d87-b878-f640700d3552"
  - "de09a43f-1c54-4a69-8573-84f340bfebd9"
  - "b14b01f6-2d44-4720-826a-5dd6b33a3ed6"
  - "40f2cc08-6c9d-45b5-90df-b26326c557a7"
  phoneSecretKey: "f3d746f00d0945c3a6edb9dc21f4b91f"

