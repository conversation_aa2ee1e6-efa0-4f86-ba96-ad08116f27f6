yc:
  redis:
    channel:
      name: channel
      addr: redis-cnlf2vxmzeuyykgna.redis.ivolces.com:6379 #todo 修改实际使用redis地址
      password: "Yangcong345"
      db: 0
      keyPrefix: "channel:"
  pg:
    channel:
      name: channel #todo 修改实际使用redis地址
      host: bj-channel.rds-pg.ivolces.com
      port: 5432
      user: channel
      password: unitedcha
      db: channel
      sslMode: disable
      maxIdleConns: 5
      maxOpenConns: 10
      connMaxLifetime: 60s
      debug: true
      singularTable: true
      disableMetric: false
      slowThreshold: 0.5s
    channeldata:
      name: channel-data #todo 修改实际使用redis地址
      host: bj-channel.rds-pg.ivolces.com
      port: 5432
      user: channel
      password: unitedcha
      db: channel_data
      sslMode: disable
      maxIdleConns: 5
      maxOpenConns: 10
      connMaxLifetime: 60s
      debug: true
      singularTable: true
      disableMetric: false
      slowThreshold: 0.5s
  nacos:
    host: ${NACOS_SD_HOST}
    port: ${NACOS_SD_PORT}
    username: ${NACOS_SD_ACCOUNT}
    password: ${NACOS_SD_PASSWD}
  phoneSecretKey: "f3d746f00d0945c3a6edb9dc21f4b91f"

ratelimit:
  window: 10
  bucket: 100
  cpuThreshold: 800
yapiProjectId: "1234"

ycoss:
  accessToken: "5d8a0b8c-e824-47a4-b7f3-cf5a368ad1bd"
  bucket: "school-res"
  domain: "https://school-res.yangcong345.com"

feishu:
  approvalCode: "D1BD0742-A09C-4EA6-B070-0802220A8338"
  invoiceApprovalCode: "7F1DB803-16F9-46C9-BABD-EAFA312ADB9C"
  entryInvoiceApprovalCode: "7FB5AB53-04B0-4793-A14B-7CB359F0FCF0"
  offlineOrderApprovalCode: "8A59FC51-47E7-4665-A899-8D4578BE41F4"
  userId: "77a7fc5g"
  openId: "ou_00c5f9697cf4f70bc31e90495f66c404"
  departmentId: "69d12g579df5db45"
  clientId: "microServiceKey-pro"
  clientSecret: "microService-gearbox-pro"
  agencyUserAlert: "https://open.feishu.cn/open-apis/bot/v2/hook/750dfda6-2f6a-46be-9ce0-902dd963db0f"
jwt:
  secret: "IVawunxdozVHiqS7MME22"
  shadowSecret: "super MUZH"
  expiresIn: "720h"

cronTasks:
  notify: FEISHU # 通知方式,目前支持FEISHU,CONSOLE,如果配置了该参数,代表下面的任务全部是根据该参数进行通知
  feishu: # 飞书通知配置
    url: https://open.feishu.cn/open-apis/bot/v2/hook/fe27e07d-cb82-42a6-baaa-6bb5cacaecf7 # 飞书通知url
    title: 定时任务 # 飞书通知标题
  task1:
    name: customer_event # 任务1的名称,需要保证唯一
    cron: "0 0 0 20 * *" # 任务1的定时周期
    mode: default # 运行模式,default:到时间就开一个新的协程处理任务; skip:该执行的时候,上一个还没结束,就跳过本次运行; delay:该执行的时候,上一个还没结束,就等着,直到上一个执行完后续继续执行
  refreshGood:
    name: refreshGood # 任务1的名称,需要保证唯一
    cron: "0 */10 * * * *" # 任务1的定时周期
    mode: skip # 运行模式,default:到时间就开一个新的协程处理任务; skip:该执行的时候,上一个还没结束,就跳过本次运行; delay:该执行的时候,上一个还没结束,就等着,直到上一个执行完后续继续执行
hiddenStudyTourAgency: 176 # 隐藏研学的代理商id
goodKindIdLevel2For998:
  - "4a6cce87-3f87-4897-b4c1-ccefd214ded6"
  - "0cc0513f-e215-4da8-9e8f-613feda10598"
  - "aeaf5151-70ea-43e5-a549-c776cb7d6aa1"
  - "69be17f9-ce57-48b0-a011-95a607abb175"
  - "b92b24a3-8f27-4e9c-a9d6-ca184fb70d9e"
