module gitlab.yc345.tv/backend/channel

go 1.18

require (
	github.com/envoyproxy/protoc-gen-validate v1.0.4
	github.com/fsnotify/fsnotify v1.7.0
	github.com/go-kratos/aegis v0.2.0
	github.com/go-kratos/kratos/v2 v2.7.3
	github.com/google/gnostic v0.7.0
	github.com/google/wire v0.6.0
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.20.0
	github.com/nacos-group/nacos-sdk-go v1.1.4
	github.com/satori/go.uuid v1.2.0
	github.com/shopspring/decimal v1.4.0
	github.com/sirupsen/logrus v1.9.3
	github.com/spf13/cast v1.6.0
	github.com/spf13/viper v1.15.0
	gitlab.yc345.tv/backend/go-logger v1.2.15
	gitlab.yc345.tv/security-and-payment/tracing v1.1.6
	go.uber.org/automaxprocs v1.6.0
	google.golang.org/grpc v1.64.0
	google.golang.org/protobuf v1.34.1
	gopkg.in/natefinch/lumberjack.v2 v2.2.1
)

require (
	github.com/bits-and-blooms/bloom v2.0.3+incompatible
	github.com/coocood/freecache v1.2.4
	github.com/go-kratos/kratos/contrib/registry/nacos/v2 v2.0.0-20240516020449-fbac5fa25e7a
	github.com/go-redis/redis/v8 v8.11.5
	github.com/go-resty/resty/v2 v2.13.1
	github.com/gogf/gf/v2 v2.4.1
	github.com/golang-jwt/jwt v3.2.2+incompatible
	github.com/golang-module/carbon/v2 v2.4.1
	github.com/google/uuid v1.6.0
	github.com/jackc/pgtype v1.14.3
	github.com/jinzhu/copier v0.3.5
	github.com/lib/pq v1.10.9
	github.com/samber/lo v1.49.1
	github.com/stretchr/testify v1.10.0
	github.com/tealeg/xlsx v1.0.5
	github.com/xuri/excelize/v2 v2.7.1
	gitlab.yc345.tv/backend/good v1.12.16
	gitlab.yc345.tv/backend/identity v1.0.15
	gitlab.yc345.tv/backend/revenue-strategy/v4 v4.3.210
	gitlab.yc345.tv/backend/utils/v2 v2.5.1-0.20250515074553-27beabf07ad6
	gitlab.yc345.tv/backend/yccrypt/go v0.0.6
	go.uber.org/zap v1.27.0
	golang.org/x/sync v0.11.0
	google.golang.org/genproto/googleapis/api v0.0.0-20240521202816-d264139d666e
	gorm.io/datatypes v1.2.0
	gorm.io/gen v0.3.25
	gorm.io/gorm v1.26.0
	gorm.io/plugin/dbresolver v1.6.0
)

require (
	github.com/BurntSushi/toml v1.2.1 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/bsm/redislock v0.7.2 // indirect
	github.com/bytedance/sonic v1.10.2 // indirect
	github.com/chenzhuoyu/base64x v0.0.0-20230717121745-296ad89f973d // indirect
	github.com/chenzhuoyu/iasm v0.9.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/fatih/color v1.17.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/go-kratos/kratos/contrib/metrics/prometheus/v2 v2.0.0-20230508130540-56777ee655d3 // indirect
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/gomodule/redigo v1.9.2 // indirect
	github.com/google/gnostic-models v0.6.9-0.20230804172637-c7be7c783f49 // indirect
	github.com/google/subcommands v1.2.0 // indirect
	github.com/iancoleman/strcase v0.3.0 // indirect
	github.com/jackc/puddle/v2 v2.2.1 // indirect
	github.com/klauspost/cpuid/v2 v2.2.4 // indirect
	github.com/mattn/go-sqlite3 v2.0.3+incompatible // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.4 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_golang v1.17.0 // indirect
	github.com/prometheus/client_model v0.5.0 // indirect
	github.com/prometheus/common v0.44.0 // indirect
	github.com/prometheus/procfs v0.11.1 // indirect
	github.com/robfig/cron/v3 v3.0.0 // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/spaolacci/murmur3 v0.0.0-20180118202830-f09979ecbc72 // indirect
	github.com/twitchyliquid64/golang-asm v0.15.1 // indirect
	github.com/willf/bitset v1.1.11 // indirect
	gitlab.yc345.tv/backend/go-notice v1.0.1 // indirect
	go.opentelemetry.io/otel/metric v1.24.0 // indirect
	golang.org/x/arch v0.3.0 // indirect
	golang.org/x/mod v0.17.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240521202816-d264139d666e // indirect
	gorm.io/driver/mysql v1.5.7 // indirect
	gorm.io/hints v1.1.0 // indirect
	gorm.io/plugin/prometheus v0.1.0 // indirect
)

require (
	github.com/aliyun/alibaba-cloud-sdk-go v1.61.18 // indirect
	github.com/buger/jsonparser v1.1.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/gin-contrib/sse v0.1.0 // indirect
	github.com/gin-gonic/gin v1.9.1
	github.com/go-errors/errors v1.5.1 // indirect
	github.com/go-logr/logr v1.4.1 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-playground/form/v4 v4.2.1 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.16.0 // indirect
	github.com/goccy/go-json v0.10.2 // indirect
	github.com/gorilla/mux v1.8.1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/imdario/mergo v0.3.16 // indirect
	github.com/jackc/pgio v1.0.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a // indirect
	github.com/jackc/pgx/v5 v5.5.5 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/labstack/echo/v4 v4.11.4 // indirect
	github.com/labstack/gommon v0.4.2 // indirect
	github.com/leodido/go-urn v1.2.4 // indirect
	github.com/lufia/plan9stats v0.0.0-20230326075908-cb1d2100619a // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/pelletier/go-toml/v2 v2.2.1 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/power-devops/perfstat v0.0.0-20221212215047-62379fc7944b // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/shirou/gopsutil/v3 v3.23.12 // indirect
	github.com/spf13/afero v1.11.0 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/spf13/pflag v1.0.5 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/ugorji/go/codec v1.2.11 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasttemplate v1.2.2 // indirect
	github.com/xuri/efp v0.0.0-20220603152613-6918739fd470 // indirect
	github.com/xuri/nfp v0.0.0-20220409054826-5e722a1d9e22 // indirect
	github.com/yusufpapurcu/wmi v1.2.3 // indirect
	gitlab.yc345.tv/teacher/golang-base v1.2.22
	go.opentelemetry.io/otel v1.24.0 // indirect
	go.opentelemetry.io/otel/exporters/jaeger v1.4.1 // indirect
	go.opentelemetry.io/otel/sdk v1.24.0 // indirect
	go.opentelemetry.io/otel/trace v1.24.0 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/crypto v0.23.0 // indirect
	golang.org/x/exp v0.0.0-20240525044651-4c93da0ed11d
	golang.org/x/net v0.25.0 // indirect
	golang.org/x/sys v0.26.0 // indirect
	golang.org/x/text v0.21.0 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/driver/postgres v1.5.4
)
