package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/go-kratos/kratos/contrib/registry/nacos/v2"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/env"
	"github.com/go-kratos/kratos/v2/config/file"
	_ "github.com/go-kratos/kratos/v2/encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.yc345.tv/backend/channel/internal/conf"
	"gitlab.yc345.tv/backend/channel/internal/data"
	"gitlab.yc345.tv/backend/channel/internal/server"
	goLogger "gitlab.yc345.tv/backend/go-logger/logger"
	"gitlab.yc345.tv/backend/utils/v2/observer"
	tracingcommon "gitlab.yc345.tv/security-and-payment/tracing/common"
	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Version is the version of the compiled software.
	Version string
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)

//nolint:gochecknoinits
func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf default.yaml")
}

func newApp(c *conf.Bootstrap, r *nacos.Registry, logger log.Logger, hs *http.Server, cronServer *server.Cron) *kratos.App {
	options := []kratos.Option{
		kratos.ID(id),
		kratos.Name(c.Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			hs,
			cronServer,
			observer.NewServer(),
		),
	}
	if c.GetEnv() != conf.GO_ENV_local {
		options = append(options, kratos.Registrar(r))
	}
	return kratos.New(options...)
}

func main() {
	flag.Parse()
	// 加载配置
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf+"/default.yaml"),
			file.NewSource(fmt.Sprintf("%s/%s.yaml", flagconf, os.Getenv("GO_ENV"))),
			env.NewSource(""),
		),
	)
	if err := c.Load(); err != nil {
		panic(err)
	}
	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	os.Setenv(yccrypt.YCCryptKeyEnv, bc.Yc.PhoneSecretKey)
	os.Setenv(yccrypt.YCCryptSegmentEnv, "4")

	// 初始化日志
	l := log.With(goLogger.GetKratosLogger(),
		"service.id", id,
		"service.name", bc.Name,
		"service.version", Version,
		"trace_id", tracing.TraceID(),
		"span_id", tracing.SpanID(),
	)
	// 初始化链路追踪
	_ = tracingcommon.Init(tracingcommon.SetupConfig{
		ServiceName: bc.Name + ".teacherschool",
		Version:     Version,
	})
	defer tracingcommon.Shotdown()

	// 初始化app之前，先初始化data init内的。（app有依赖于这个init）
	err := data.Init(&bc)
	if err != nil {
		panic(err)
	}

	// app初始化
	app, cleanup, err := initApp(&bc, bc.Yc, bc.Yc.Redis[bc.Name], bc.Server, l, bc.CronTasks)
	if err != nil {
		panic(err)
	}

	// 配置文件初始化
	err = conf.StartConfig(flagconf)
	if err != nil {
		panic(err)
	}
	defer cleanup()
	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
